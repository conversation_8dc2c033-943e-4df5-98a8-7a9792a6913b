import type { Metadata } from "next"
import TestimonialsClient from "./testimonials-client"

export const metadata: Metadata = {
  title: "Client Testimonials | Aneeko Rapha Healthcare Services",
  description:
    "Read what clients are saying about Aneeko Rapha Healthcare Services. Real testimonials from families who have experienced our exceptional home care services.",
  keywords: [
    "client testimonials",
    "home care reviews",
    "Aneeko Rapha testimonials",
    "senior care reviews",
    "home health care testimonials",
    "family testimonials",
    "care reviews",
  ],
  alternates: {
    canonical: "/testimonials",
  },
  openGraph: {
    title: "Client Testimonials | Aneeko Rapha Healthcare Services",
    description:
      "Read what clients are saying about Aneeko Rapha Healthcare Services. Real testimonials from families who have experienced our exceptional home care services.",
    url: "https://araphahealthcare.com/testimonials",
  },
}

export default function TestimonialsPage() {
  return <TestimonialsClient />
}
