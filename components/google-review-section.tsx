"use client"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ExternalLink, QrCode } from "lucide-react"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

export default function GoogleReviewSection() {
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  return (
    <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
      <div className="px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]">
        <motion.div initial="hidden" animate="visible" variants={fadeIn}>
          <div className="text-center mb-12">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-secondary/20 text-secondary rounded-full text-sm font-medium mb-4">
                Share Your Experience
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Leave Us a Google Review
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              Your feedback helps us improve our services and helps other families find the care they need.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 items-center max-w-4xl mx-auto">
            <motion.div variants={fadeInUp}>
              <Card className="border border-gray-100 dark:border-gray-800 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden">
                <CardContent className="p-8">
                  <div className="text-center">
                    <div className="w-16 h-16 rounded-full bg-secondary/20 flex items-center justify-center mx-auto mb-4">
                      <Star className="h-8 w-8 text-secondary" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">Rate Your Experience</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      Help other families by sharing your experience with Illiana's Angel Healthcare on Google.
                    </p>

                    <div className="space-y-4">
                      <a
                        href="https://g.page/r/[NEW_GOOGLE_BUSINESS_ID]/review"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block"
                      >
                        <Button className="w-full bg-primary hover:bg-primary-dark text-white rounded-full py-3 transition-all duration-300 hover:shadow-lg flex items-center justify-center gap-2">
                          <Star className="h-4 w-4" />
                          Leave a Google Review
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </a>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full border-gray-200 dark:border-gray-700 rounded-full py-3 flex items-center justify-center gap-2"
                          >
                            <QrCode className="h-4 w-4" />
                            Show QR Code
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle className="text-center">Scan to Leave a Review</DialogTitle>
                          </DialogHeader>
                          <div className="flex flex-col items-center space-y-4">
                            <div className="bg-white p-4 rounded-lg shadow-md">
                              <Image
                                src="https://res.cloudinary.com/dvauarkh6/image/upload/v1747987437/unnamed_hmyls0.png"
                                alt="Google Review QR Code"
                                width={200}
                                height={200}
                                className="rounded-lg"
                              />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                              Scan this QR code with your phone camera to leave a Google review
                            </p>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={fadeInUp} className="relative">
              <div className="w-full h-0 pb-[75%] relative rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="https://res.cloudinary.com/dvauarkh6/image/upload/v1747919323/pexels-cottonbro-7579831_isaqwi.jpg"
                  alt="Happy client with caregiver"
                  fill
                  className="object-cover rounded-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6">
                  <p className="text-white text-lg font-medium">Your feedback matters to us</p>
                </div>
              </div>

              <div className="absolute -top-8 -right-8 w-20 h-20 rounded-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg flex items-center justify-center">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-3 w-3 text-secondary fill-current" />
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
