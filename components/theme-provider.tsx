"use client"

import { useEffect, useState } from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps } from "next-themes"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false)

  // Make sure the component is mounted before rendering children
  useEffect(() => {
    setMounted(true)
  }, [])

  // Force dark mode class on body when theme changes
  useEffect(() => {
    const updateDarkModeClass = () => {
      const isDarkMode = document.documentElement.classList.contains("dark")
      if (isDarkMode) {
        document.body.classList.add("dark")
      } else {
        document.body.classList.remove("dark")
      }
    }

    // Set up a mutation observer to watch for class changes on html element
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          updateDarkModeClass()
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })

    // Initial check
    updateDarkModeClass()

    return () => {
      observer.disconnect()
    }
  }, [mounted])

  if (!mounted) {
    return null
  }

  return (
    <NextThemesProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange {...props}>
      {children}
    </NextThemesProvider>
  )
}
