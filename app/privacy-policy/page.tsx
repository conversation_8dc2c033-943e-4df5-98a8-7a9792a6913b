"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { ArrowLeft, Shield, Phone, Mail, MessageSquare } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"

export default function PrivacyPolicyPage() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Privacy Policy"
        subtitle="Your privacy and data protection are our priority"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747926314/pexels-olly-789822_1_vn9fiz.jpg"
        imageAlt="Privacy and security concept"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-4xl mx-auto">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-8 mb-8">
              <div className="flex items-center gap-3 mb-6">
                <Shield className="h-8 w-8 text-primary" />
                <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Privacy Policy – ANEEKO RAPHA HEALTHCARE SERVICES
                </h1>
              </div>

              <p className="text-gray-600 dark:text-gray-300 mb-8 text-lg">
                ANEEKO RAPHA HEALTHCARE SERVICES respects your privacy and is committed to protecting it through this Privacy
                Policy. This document outlines how we collect, use, disclose, and safeguard your information, including
                phone numbers used for SMS communications.
              </p>

              <div className="space-y-8">
                {/* Information We Collect */}
                <section>
                  <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100 mb-4 flex items-center gap-2">
                    <Mail className="h-6 w-6 text-primary" />
                    Information We Collect
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    We may collect the following types of information:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-600 dark:text-gray-300">
                    <li>Personal information such as your name, email address, phone number, and mailing address.</li>
                    <li>
                      Information automatically collected when you visit our website, including cookies and usage data.
                    </li>
                    <li>Information provided by you when opting into SMS messaging.</li>
                  </ul>
                </section>

                {/* Use of Information */}
                <section>
                  <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100 mb-4">Use of Information</h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">The information we collect may be used for:</p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-600 dark:text-gray-300">
                    <li>Providing and maintaining our services.</li>
                    <li>Communicating with you, including via SMS, if you have opted in.</li>
                    <li>Processing transactions and delivering services you request.</li>
                    <li>Ensuring compliance with regulatory and legal obligations.</li>
                  </ul>
                </section>
              </div>
            </div>

            {/* SMS Terms & Conditions */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-8">
              <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100 mb-6 flex items-center gap-2">
                <MessageSquare className="h-6 w-6 text-primary" />
                SMS Terms & Conditions
              </h2>

              <div className="space-y-6">
                {/* SMS Consent Communication */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">
                    1. SMS Consent Communication
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    The information (************* obtained as part of the SMS consent process will not be shared with
                    third parties for marketing purposes.
                  </p>
                </section>

                {/* Types of SMS Communications */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">
                    2. Types of SMS Communications
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-3">
                    If you have consented to receive text messages from ANEEKO RAPHA HEALTHCARE SERVICES, you may receive
                    messages related to the following:
                  </p>
                  <ul className="list-disc pl-6 space-y-1 text-gray-600 dark:text-gray-300 mb-4">
                    <li>Appointment reminders</li>
                    <li>Follow-up messages</li>
                    <li>Billing inquiries</li>
                    <li>Services updates</li>
                  </ul>
                  <div className="bg-primary-light/20 dark:bg-primary-dark/20 p-4 rounded-lg border border-primary/20">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      <strong>Example:</strong> "Hello, this is a friendly reminder of your upcoming assessment with
                      ANEEKO RAPHA HEALTHCARE SERVICES at home on [Date] at [Time]. Reply STOP to opt out of SMS messaging at
                      any time."
                    </p>
                  </div>
                </section>

                {/* Message Frequency */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">3. Message Frequency</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Message frequency may vary depending on the type of communication. For example, you may receive up
                    to 5 SMS messages per week related to your appointments, follow-ups, billing inquiries, and service
                    updates.
                  </p>
                </section>

                {/* Potential Fees */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">
                    4. Potential Fees for SMS Messaging
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Please note that standard message and data rates may apply, depending on your carrier's pricing
                    plan. These fees may vary if the message is sent domestically or internationally.
                  </p>
                </section>

                {/* Opt-In Method */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">5. Opt-In Method</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    You may opt-in to receive SMS messages from ANEEKO RAPHA HEALTHCARE SERVICES in the following way:
                  </p>
                  <ul className="list-disc pl-6 text-gray-600 dark:text-gray-300">
                    <li>By submitting an online form</li>
                  </ul>
                </section>

                {/* Opt-Out Method */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">6. Opt-Out Method</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    You can opt out of receiving SMS messages at any time. To do so:
                  </p>
                  <ul className="list-disc pl-6 space-y-1 text-gray-600 dark:text-gray-300">
                    <li>Simply reply "STOP" to any SMS message you receive.</li>
                    <li>Alternatively, you can contact us directly to request removal from our messaging list.</li>
                  </ul>
                </section>

                {/* Help */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">7. Help</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">If you are experiencing any issues:</p>
                  <ul className="list-disc pl-6 space-y-1 text-gray-600 dark:text-gray-300">
                    <li>Reply with the keyword HELP</li>
                    <li>
                      Or, visit:{" "}
                      <Link href="/terms-conditions" className="text-primary hover:underline">
                        privacy-policy
                      </Link>
                    </li>
                  </ul>
                </section>

                {/* Additional Options */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">8. Additional Options</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    If you do not wish to receive SMS messages, you can choose not to check the SMS consent box on our
                    forms.
                  </p>
                </section>

                {/* Standard Messaging Disclosures */}
                <section>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">
                    9. Standard Messaging Disclosures
                  </h3>
                  <ul className="list-disc pl-6 space-y-1 text-gray-600 dark:text-gray-300">
                    <li>Message and data rates may apply.</li>
                    <li>You can opt out at any time by texting "STOP."</li>
                    <li>
                      For assistance, text "HELP" or visit our{" "}
                      <Link href="/privacy-policy" className="text-primary hover:underline">
                        Privacy Policy
                      </Link>{" "}
                      and{" "}
                      <Link href="/terms-conditions" className="text-primary hover:underline">
                        Terms and Conditions
                      </Link>{" "}
                      pages.
                    </li>
                    <li>Message frequency may vary.</li>
                  </ul>
                </section>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-primary-light/50 dark:bg-primary-dark/20 p-8 rounded-xl border border-primary/10 dark:border-primary-light/10 mt-8">
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Contact Information</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                    ANEEKO RAPHA HEALTHCARE SERVICES
                  </h3>
                  <div className="space-y-2 text-gray-600 dark:text-gray-300">
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <strong>Phone:</strong>{" "}
                      <a href="tel:+16822289234" className="text-primary hover:underline">
                        (*************
                      </a>
                    </p>
                   
                    <p className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <strong>Email:</strong>{" "}
                      <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                        <EMAIL>
                      </a>
                    </p>
                    
                    
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Last Updated</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    This Privacy Policy was last updated on January 14, 2025. We may update this policy from time to
                    time to reflect changes in our practices or for other operational, legal, or regulatory reasons.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
