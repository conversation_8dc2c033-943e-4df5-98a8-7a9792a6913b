/**
 * FIRST half of the blog-post catalogue.
 * -------------------------------------------------
 * 1.  Define BlogPost type
 * 2.  Export `blogPostsPart1`  (named)
 * 3.  Re-export it as the default export
 */

export interface BlogPost {
  id: string
  title: string
  date: string
  author: string
  readTime: string
  excerpt: string
  content: string
  image: string
  icon: string
}

export const blogPostsPart1: BlogPost[] = [
  {
    id: "healthy-aging-at-home",
    title: "Healthy Aging at Home: Practical Tips",
    date: "September 12, 2023",
    author: "<PERSON>",
    readTime: "7 min read",
    excerpt: "Simple lifestyle changes can make aging in place safer and more enjoyable.",
    content: "<p>Aging at home is possible with a few practical adjustments...</p>",
    image: "/images/blog/healthy-aging.jpg",
    icon: "Smile",
  },
  {
    id: "caregiver-burnout-signs",
    title: "Recognizing & Preventing Care-giver Burnout",
    date: "October 02, 2023",
    author: "<PERSON>",
    readTime: "6 min read",
    excerpt: "Family caregivers often ignore their own well-being. Learn the warning signs.",
    content: "<p>Caring for a loved one is rewarding but also exhausting...</p>",
    image: "/images/blog/caregiver-burnout.jpg",
    icon: "Heart",
  },
  {
    id: "meal-prep-for-seniors",
    title: "Nutritious Meal Prep Ideas for Seniors",
    date: "October 18, 2023",
    author: "Olivia Green",
    readTime: "8 min read",
    excerpt: "Quick, healthy recipes that support senior nutrition and independence.",
    content: "<p>Good nutrition is the cornerstone of healthy aging...</p>",
    image: "/images/blog/meal-prep.jpg",
    icon: "Utensils",
  },
]

export default blogPostsPart1
