@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 1 65% 40%;
    --primary-foreground: 0 0% 98%;

    --secondary: 118 32% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 0 0% 46%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 1 65% 63%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 1 65% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 118 32% 35%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 0 0% 46%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 1 65% 68%;
  }

  html,
  body {
    overflow-x: hidden;
    position: relative;
    width: 100%;
    max-width: 100vw;
    font-family: var(--font-nunito-sans), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  }

  /* Fix for double scrollbar issue */
  body {
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Ensure all containers respect boundaries */
  #__next,
  main,
  .container {
    overflow-x: hidden;
    max-width: 100%;
  }

  /* Typography styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.01em;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
  }

  h2 {
    font-size: 2rem;
    font-weight: 600;
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
  }

  p {
    line-height: 1.7;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Enhanced mobile scrolling */
@media (max-width: 768px) {
  .custom-scrollbar {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  /* Ensure content doesn't get cut off on iOS */
  .custom-scrollbar {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Fix for iOS momentum scrolling */
.overflow-y-auto,
.overflow-y-scroll {
  -webkit-overflow-scrolling: touch;
}

/* Prevent body scroll when modal is open */
body.no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Blog content styling */
.prose h3 {
  color: hsl(240, 50%, 60%);
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.dark .prose h3 {
  color: hsl(240, 60%, 75%);
}

.prose p {
  margin-bottom: 1.25em;
  line-height: 1.7;
  color: #4b5563;
}

.dark .prose p {
  color: #d1d5db;
}

.prose ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-bottom: 1.25em;
}

.prose li {
  margin-bottom: 0.5em;
  color: #4b5563;
}

.dark .prose li {
  color: #d1d5db;
}

.prose a {
  color: hsl(240, 50%, 60%);
  text-decoration: underline;
  text-underline-offset: 2px;
}

.dark .prose a {
  color: hsl(240, 60%, 75%);
}

.prose a:hover {
  text-decoration: none;
}

.prose blockquote {
  border-left: 4px solid hsl(240, 50%, 60%);
  padding-left: 1em;
  font-style: italic;
  margin: 1.5em 0;
}

.dark .prose blockquote {
  border-left-color: hsl(240, 60%, 75%);
}

/* Fix for fixed position elements causing overflow */
.fixed {
  max-width: 100vw;
}

/* Fix for dropdown scrollbar issue */
body.dropdown-open {
  overflow-x: hidden;
  width: 100%;
}

/* Ensure dropdown doesn't cause horizontal scrolling */
.dropdown-container {
  max-width: 100vw;
  overflow-x: visible;
}

/* Hero section spacing */
.hero-section {
  margin-top: 140px;
}

@media (min-width: 768px) {
  .hero-section {
    margin-top: 150px;
  }
}

/* Modern button styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modern-button:hover::after {
  transform: translateX(0);
}
