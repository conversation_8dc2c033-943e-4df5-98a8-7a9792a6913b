/**
 * CSV generator (called by /api/blog-csv).
 * Now imports the unified `blogPosts` list.
 */
import { blogPosts } from "./blog-data"
import { writeFile } from "fs/promises"
import path from "path"

export async function exportBlogsToCSV(): Promise<string> {
  if (blogPosts.length === 0) return ""

  const header = "id,title,date,author,readTime,excerpt"
  const rows = blogPosts.map((p) =>
    [p.id, csvEscape(p.title), csvEscape(p.date), csvEscape(p.author), p.readTime, csvEscape(p.excerpt)].join(","),
  )

  const csv = [header, ...rows].join("\n")

  // Write the file so it can be downloaded directly
  const outFile = path.join(process.cwd(), "public", "blog-posts.csv")
  await writeFile(outFile, csv, "utf8")

  return csv
}

/* ---------------- helpers --------------- */
function csvEscape(value: string) {
  const safe = value.replace(/"/g, '""')
  return safe.includes(",") ? `"${safe}"` : safe
}

/* default export for convenience */
export default exportBlogsToCSV
