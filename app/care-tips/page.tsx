import type { Metadata } from "next"
import CareClient from "./care-client"

export const metadata: Metadata = {
  title: "Care Tips | Aneeko Rapha Healthcare Services",
  description:
    "Helpful care tips and information for seniors and families. Learn about senior care, respite care, and other important healthcare topics.",
  keywords: [
    "care tips",
    "senior care tips",
    "respite care information",
    "healthcare guidance",
    "family caregiving",
    "home care tips",
  ],
  alternates: {
    canonical: "/care-tips",
  },
  openGraph: {
    title: "Care Tips | Aneeko Rapha Healthcare Services",
    description:
      "Helpful care tips and information for seniors and families. Learn about senior care, respite care, and other important healthcare topics.",
    url: "https://araphahealthcare.com/care-tips",
  },
}

export default function CareGuide() {
  return <CareClient />
}
