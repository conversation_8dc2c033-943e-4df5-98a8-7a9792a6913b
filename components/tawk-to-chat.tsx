"use client"

import { useEffect } from "react"

export function TawkToChat() {
  useEffect(() => {
    // Tawk.to script
    var Tawk_API: any = window.Tawk_API || {}
    var Tawk_LoadStart = new Date()

    const s1 = document.createElement("script")
    const s0 = document.getElementsByTagName("script")[0]

    s1.async = true
    s1.src = "https://embed.tawk.to/67b70adb607032190e3a4147/1ikhg4qgp" 
    s1.charset = "UTF-8"
    s1.setAttribute("crossorigin", "*")

    if (s0 && s0.parentNode) {
      s0.parentNode.insertBefore(s1, s0)
    }

    return () => {
      // Clean up if needed
      if (s1 && s1.parentNode) {
        s1.parentNode.removeChild(s1)
      }
    }
  }, [])

  return null
}

// Add TypeScript declaration
declare global {
  interface Window {
    Tawk_API: any
    Tawk_LoadStart: Date
  }
}
