"use client"

import React from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Heart, Users, Shield, Award, Phone, Mail, DollarSign, Clock, Star } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function CareersClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Benefits data
  const benefits = [
    {
      title: "Competitive Compensation",
      description: "We offer competitive wages and comprehensive benefits packages to attract and retain the best talent.",
      icon: DollarSign,
    },
    {
      title: "Professional Growth",
      description: "Opportunities for career advancement, continuing education, and professional development.",
      icon: Award,
    },
    {
      title: "Flexible Scheduling",
      description: "Work-life balance with flexible scheduling options to accommodate your personal needs.",
      icon: Clock,
    },
    {
      title: "Meaningful Work",
      description: "Make a real difference in people's lives while building a rewarding career in healthcare.",
      icon: Heart,
    },
    {
      title: "Supportive Team",
      description: "Join a collaborative team environment where your contributions are valued and recognized.",
      icon: Users,
    },
    {
      title: "Comprehensive Benefits",
      description: "Health insurance, paid time off, retirement plans, and other valuable benefits.",
      icon: Shield,
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Join Our Team"
        subtitle="Make a difference in the lives of seniors and adults with disabilities in Tarrant County, TX"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg"
        imageAlt="Healthcare team working together"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Career Opportunities
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Build a Rewarding Career in Home Healthcare
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Join Aneeko Rapha Healthcare Services and become part of a compassionate team dedicated to providing exceptional home healthcare services. We offer competitive compensation, comprehensive benefits, and the opportunity to make a meaningful difference in people's lives.
              </p>
            </div>
          </motion.section>

          {/* Why Work With Us Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Why Choose Aneeko Rapha Healthcare Services?
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full"></div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                    <CardContent className="p-8 text-center h-full flex flex-col">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6 flex items-center justify-center">
                        <benefit.icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed flex-grow">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Current Openings Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                Current Openings
              </Badge>
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Available Positions
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
              <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 leading-relaxed">
                Explore our current job openings and find the perfect opportunity to join our compassionate team of healthcare professionals.
              </p>
            </div>

            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Ready to Make a Difference?
              </h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                View our detailed job listings and apply for positions that match your skills and passion for helping others.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/careers/jobs">
                    <span>View Current Job Openings</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <a href="mailto:<EMAIL>">
                    <Mail className="h-5 w-5" />
                    <span>Email HR Team</span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Questions About Working With Us?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                Our HR team is here to answer any questions about career opportunities, benefits, and what it's like to work at Aneeko Rapha Healthcare Services.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2 group" asChild>
                  <a href="tel:**********">
                    <Phone className="h-5 w-5" />
                    <span>Call (*************</span>
                  </a>
                </Button>
                <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2" asChild>
                  <a href="mailto:<EMAIL>">
                    <Mail className="h-5 w-5" />
                    <span><EMAIL></span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
