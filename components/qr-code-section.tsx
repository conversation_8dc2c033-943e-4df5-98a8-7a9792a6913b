"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Star, ExternalLink, Smartphone } from "lucide-react"
import { useInView } from "react-intersection-observer"
import QRCode from "react-qr-code"

export default function QRCodeSection() {
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.1 })

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={fadeInUp}
      className="text-center"
    >
      <Card className="border border-lavender/30 shadow-lg bg-gradient-to-br from-lavender/10 to-pink/10 backdrop-blur-sm">
        <CardContent className="p-8">
          <div className="flex flex-col md:flex-row items-center gap-8">
            {/* QR Code */}
            <div className="flex-shrink-0">
              <div className="bg-white p-4 rounded-2xl shadow-lg border border-lavender/20">
                <QRCode
                  value="http://bit.ly/4jKVzF7"
                  size={160}
                  style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                  viewBox="0 0 256 256"
                />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 text-center md:text-left">
              <div className="mb-4">
                <Badge className="px-3 py-1 bg-gradient-to-r from-pink to-lavender text-white rounded-full text-sm font-medium mb-2">
                  ✨ Leave us a review
                </Badge>
              </div>

              <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-3">Scan to Review Us ✨</h3>

              <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                Your feedback helps us continue providing exceptional care to families in our community. Scan the QR
                code with your phone camera to leave a Google review!
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                <a href="http://bit.ly/4jKVzF7" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                    <Star className="h-4 w-4" />
                    <span>Leave a Google Review</span>
                    <ExternalLink className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </a>

                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Smartphone className="h-4 w-4" />
                  <span>Point your camera at the QR code</span>
                </div>
              </div>
            </div>
          </div>

          {/* Review Stats */}
          <div className="mt-8 pt-6 border-t border-lavender/20">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-center">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span className="text-gray-700 dark:text-gray-300 font-medium">5.0 Rating</span>
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                Join our satisfied families in sharing your experience
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
