import type { Metadata } from "next"
import ResourcesClient from "./resources-client"

export const metadata: Metadata = {
  title: "Resources | Aneeko Rapha Healthcare Services - Helpful Links & Information",
  description:
    "Access helpful resources and links for seniors and families in Tarrant County, TX. Find information from AARP, National Institute of Aging, Alzheimer's Association, and more.",
  keywords: [
    "senior resources Tarrant County TX",
    "healthcare resources",
    "AARP",
    "National Institute of Aging",
    "Alzheimer's Association",
    "Meals on Wheels",
    "senior care information",
    "family caregiver resources",
  ],
  alternates: {
    canonical: "/resources",
  },
  openGraph: {
    title: "Resources | Aneeko Rapha Healthcare Services - Helpful Links & Information",
    description:
      "Access helpful resources and links for seniors and families in Tarrant County, TX. Find information from AARP, National Institute of Aging, Alzheimer's Association, and more.",
    url: "https://araphahealthcare.com/resources",
  },
}

export default function ResourcesPage() {
  return <ResourcesClient />
}
