"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  Facebook,
  Twitter,
  Linkedin,
  ChevronLeft,
  ChevronRight,
  Activity,
  Brain,
  Heart,
  UserRound,
  Dumbbell,
  Laptop,
  Smile,
  AlertCircle,
  ListChecks,
  Lightbulb,
  Shield,
  ClipboardList,
  HeartHandshake,
  Home,
  Building2,
  CheckSquare,
  Scale,
  Stethoscope,
  Users,
  Wallet,
  MessageSquare,
} from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { blogPosts, type BlogPost } from "../blog-data"

// Map of icon names to Lucide React components
const iconMap = {
  Activity,
  Brain,
  Heart,
  UserRound,
  <PERSON><PERSON><PERSON>,
  Laptop,
  Smile,
  AlertCircle,
  ListChecks,
  Lightbulb,
  Shield,
  ClipboardList,
  HeartHandshake,
  Home,
  Building2,
  CheckSquare,
  Scale,
  Stethoscope,
  Users,
  Wallet,
  MessageSquare,
}

export default function BlogPostPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [post, setPost] = useState<BlogPost | null>(null)
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)

  // Function to get icon component
  const getIcon = (iconName) => {
    const IconComponent = iconMap[iconName] || Activity
    return <IconComponent className="h-full w-full text-[#382d5e] dark:text-[#4e3f7d] opacity-80" />
  }

  useEffect(() => {
    // Find the current post
    const currentPost = blogPosts.find((p) => p.id === params.id)

    if (!currentPost) {
      router.push("/blog/not-found")
      return
    }

    setPost(currentPost)

    // Get related posts (excluding current post)
    const related = blogPosts.filter((p) => p.id !== params.id).slice(0, 3)

    setRelatedPosts(related)
    setLoading(false)
  }, [params.id, router])

  if (loading) {
    return (
      <div className="min-h-screen font-sans bg-white dark:bg-gray-950 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2e7d32]"></div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen font-sans bg-white dark:bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-medium text-gray-800 dark:text-gray-100 mb-4">Post not found</h1>
          <Button asChild>
            <Link href="/blog">Return to Blog</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-[#2e7d32]/10 to-[#4caf50]/5 dark:from-[#2e7d32]/5 dark:to-[#4caf50]/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-[#ff9800]/10 to-[#ffb74d]/5 dark:from-[#ff9800]/5 dark:to-[#ffb74d]/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Main Content */}
      <main className="pt-32 pb-16 md:pt-40 md:pb-24 relative z-10">
        <div className="px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]">
          <div className="mb-8">
            <Link
              href="/blog"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-[#382d5e] dark:hover:text-[#4e3f7d] transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Blog Post Header */}
            <div className="mb-8">
              <Badge className="mb-4 bg-[#f0edf7] text-[#382d5e] hover:bg-[#f0edf7] hover:text-[#382d5e]">
                Healthcare
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">{post.title}</h1>
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{post.date}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  <span>{post.readTime}</span>
                </div>
              </div>
            </div>

            {/* Featured Icon */}
            <div className="mb-8 rounded-xl overflow-hidden bg-[#f0edf7] dark:bg-[#2a2147]/20 flex items-center justify-center p-12">
              <div className="w-32 h-32">{getIcon(post.icon)}</div>
            </div>

            {/* Blog Content */}
            <div className="prose prose-lg dark:prose-invert max-w-none mb-12">
              <div dangerouslySetInnerHTML={{ __html: post.content }} />
            </div>

            {/* Share Links */}
            <div className="border-t border-b border-gray-200 dark:border-gray-700 py-6 mb-12">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <p className="font-medium text-gray-700 dark:text-gray-300">Share this article:</p>
                <div className="flex gap-3">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Facebook className="h-4 w-4" />
                    <span className="sr-only">Share on Facebook</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Twitter className="h-4 w-4" />
                    <span className="sr-only">Share on Twitter</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Linkedin className="h-4 w-4" />
                    <span className="sr-only">Share on LinkedIn</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Related Posts */}
            {relatedPosts.length > 0 && (
              <div className="mb-12">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Related Articles</h2>
                <div className="grid md:grid-cols-3 gap-6">
                  {relatedPosts.map((relatedPost) => (
                    <Card
                      key={relatedPost.id}
                      className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-lg transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                    >
                      <CardContent className="p-0 flex flex-col h-full">
                        <div className="relative h-40 w-full bg-[#f0edf7] dark:bg-[#2a2147]/20 flex items-center justify-center p-4">
                          <div className="w-16 h-16">{getIcon(relatedPost.icon)}</div>
                        </div>
                        <div className="p-4 flex flex-col flex-grow">
                          <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2 line-clamp-2">
                            {relatedPost.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                            {relatedPost.excerpt}
                          </p>
                          <Link href={`/blog/${relatedPost.id}`} className="mt-auto">
                            <Button
                              variant="ghost"
                              className="w-full justify-between p-0 h-auto hover:bg-transparent hover:text-[#382d5e] dark:hover:text-[#4e3f7d] group"
                            >
                              Read More
                              <ChevronRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                className="border-gray-200 dark:border-gray-700 hover:border-[#382d5e] dark:hover:border-[#4e3f7d] hover:text-[#382d5e] dark:hover:text-[#4e3f7d]"
              >
                <Link href="/blog" className="flex items-center">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to All Articles
                </Link>
              </Button>
              <Button className="bg-[#c84449] hover:bg-[#b13339] text-white">
                <Link href="/consultation">Book a Consultation</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
