import type React from "react"

interface EmailTemplateProps {
  name: string
  message: string
  formType?: "contact" | "consultation" | "referral"
  formData?: Record<string, any>
}

export const EmailTemplate: React.FC<EmailTemplateProps> = ({ name, message, formType = "contact", formData = {} }) => {
  // Use absolute URL for logo - you'll need to replace this with your actual domain
  const logoUrl = "https://journey-of-care.com/images/journey-of-care-logo.png"

  // Format form data for admin email
  const formDataHtml = Object.entries(formData)
    .filter(([key]) => !["message"].includes(key))
    .map(([key, value]) => {
      const formattedKey = key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase())
        .replace(/([a-z])([A-Z])/g, "$1 $2")

      return `<tr>
        <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568; font-family: Arial, sans-serif;">${formattedKey}</td>
        <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; color: #4a5568; font-family: Arial, sans-serif;">${value}</td>
      </tr>`
    })
    .join("")

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Journey of Care</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f8fdf9;">
      <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9;">
        <tr>
          <td align="center" style="padding: 20px;">
            <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 600px;">
              
              <!-- Header -->
              <tr>
                <td align="center" style="background-color: #E6E6FA; padding: 30px 20px; border-radius: 12px 12px 0 0;">
                  <img src="${logoUrl}" alt="Journey of Care" style="max-width: 200px; height: auto; display: block;" />
                </td>
              </tr>
              
              <!-- Content -->
              <tr>
                <td style="padding: 30px;">
                  <h2 style="color: #333; font-family: Arial, sans-serif; margin: 0 0 20px 0;">Hello ${name},</h2>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">${message}</p>
                  
                  ${
                    formType === "contact"
                      ? `
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">Thank you for reaching out to Journey of Care. We've received your message and will get back to you as soon as possible, typically within 24-48 hours.</p>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">If you have an urgent matter, please call us directly at <strong>(*************</strong>.</p>
                  `
                      : formType === "consultation"
                        ? `
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">Thank you for requesting a consultation with Journey of Care. We've received your request and will contact you shortly to confirm your appointment.</p>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">If you need to make any changes to your request, please call us at <strong>(*************</strong>.</p>
                  `
                        : `
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">Thank you for your referral to Journey of Care. We appreciate your trust in our services and will reach out to your referral soon.</p>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">If you have any questions about our referral program, please contact us at <strong>(*************</strong>.</p>
                  `
                  }
                  
                  <!-- Contact Info Box -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9; border: 1px solid #E6E6FA; border-radius: 12px; margin-top: 30px;">
                    <tr>
                      <td style="padding: 25px;">
                        <h3 style="color: #8B5A8C; margin: 0 0 15px 0; font-size: 18px; font-family: Arial, sans-serif;">Journey of Care</h3>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Phone:</strong> (*************</p>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Email:</strong> <EMAIL></p>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Service Area:</strong> Conroe, TX & Surrounding Communities</p>
                        <p style="margin: 15px 0 5px 0; color: #8B5A8C; font-weight: 600; font-family: Arial, sans-serif;">Your Peace of Mind Is Our Promise of Care</p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              
              <!-- Footer -->
              <tr>
                <td style="background-color: #f8f4ff; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; border-top: 1px solid #E6E6FA;">
                  <p style="margin: 0 0 10px 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">&copy; ${new Date().getFullYear()} Journey of Care. All rights reserved.</p>
                  <p style="margin: 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">This is an automated message, please do not reply directly to this email.</p>
                </td>
              </tr>
              
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `
}

export const AdminEmailTemplate: React.FC<EmailTemplateProps> = ({
  name,
  message,
  formType = "contact",
  formData = {},
}) => {
  const logoUrl = "https://journey-of-care.com/images/journey-of-care-logo.png"

  // Format form data for admin email
  const formDataHtml = Object.entries(formData)
    .map(([key, value]) => {
      const formattedKey = key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase())
        .replace(/([a-z])([A-Z])/g, "$1 $2")

      return `<tr>
        <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #4a5568; font-family: Arial, sans-serif;">${formattedKey}</td>
        <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; color: #4a5568; font-family: Arial, sans-serif;">${value}</td>
      </tr>`
    })
    .join("")

  const formTypeText =
    formType === "contact"
      ? "Contact Form Submission"
      : formType === "consultation"
        ? "Consultation Request"
        : "Referral Submission"

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New ${formTypeText} - Journey of Care</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f8fdf9;">
      <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9;">
        <tr>
          <td align="center" style="padding: 20px;">
            <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 600px;">
              
              <!-- Header -->
              <tr>
                <td align="center" style="background-color: #E6E6FA; padding: 30px 20px; border-radius: 12px 12px 0 0;">
                  <img src="${logoUrl}" alt="Journey of Care" style="max-width: 200px; height: auto; display: block;" />
                </td>
              </tr>
              
              <!-- Content -->
              <tr>
                <td style="padding: 30px;">
                  <h2 style="color: #333; font-family: Arial, sans-serif; margin: 0 0 20px 0;">New ${formTypeText}</h2>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">You have received a new submission from the ${formType} form on your website.</p>
                  
                  <!-- Form Data Table -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; margin: 20px 0;">
                    <tbody>
                      ${formDataHtml}
                    </tbody>
                  </table>
                  
                  ${
                    formData.message
                      ? `
                  <h3 style="color: #8B5A8C; font-family: Arial, sans-serif; margin: 20px 0 10px 0;">Message:</h3>
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">${formData.message}</p>
                  `
                      : ""
                  }
                  
                  <!-- Contact Info Box -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9; border: 1px solid #E6E6FA; border-radius: 12px; margin-top: 30px;">
                    <tr>
                      <td style="padding: 25px;">
                        <h3 style="color: #8B5A8C; margin: 0 0 15px 0; font-size: 18px; font-family: Arial, sans-serif;">Journey of Care</h3>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Phone:</strong> (*************</p>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Email:</strong> <EMAIL></p>
                        <p style="margin: 5px 0; color: #333; font-family: Arial, sans-serif;"><strong>Service Area:</strong> Conroe, TX & Surrounding Communities</p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              
              <!-- Footer -->
              <tr>
                <td style="background-color: #f8f4ff; padding: 20px; text-align: center; border-radius: 0 0 12px 12px; border-top: 1px solid #E6E6FA;">
                  <p style="margin: 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">&copy; ${new Date().getFullYear()} Journey of Care. All rights reserved.</p>
                </td>
              </tr>
              
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `
}
