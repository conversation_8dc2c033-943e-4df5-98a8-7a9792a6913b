"use client"
import { useState, useEffect } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowRight, Phone, CheckCircle, Star } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { services } from "./new-services-data"
import { cn } from "@/lib/utils"

export default function ServicesPageClient() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<string>("")

  useEffect(() => {
    // Get the service from URL query parameter
    const serviceParam = searchParams.get("service")
    if (serviceParam && services.find(s => s.id === serviceParam)) {
      setActiveTab(serviceParam)
    } else if (services.length > 0) {
      // Default to first service if none specified
      setActiveTab(services[0].id)
    }
  }, [searchParams])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Update URL without page reload
    const newUrl = `/services?service=${value}`
    router.push(newUrl, { scroll: false })
  }

  const currentService = services.find((service) => service.id === activeTab)

  if (!currentService) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-950">
        <Header />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Service Not Found</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">The requested service could not be found.</p>
            <Link href="/services">
              <Button>View All Services</Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      <Header />

      {/* Hero Section */}
      <PageHero
        title="Our Services"
        subtitle="Comprehensive home care services tailored to your unique needs and preferences"
        imageSrc="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80"
        imageAlt="Professional caregiver providing compassionate care"
      />

      {/* Mission and Vision Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Mission */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Our Mission</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                To provide compassionate, high-quality home care services that enable individuals to maintain their independence, dignity, and quality of life in the comfort of their own homes. We are committed to delivering personalized care that meets the unique needs of each client while supporting their families with peace of mind.
              </p>
            </motion.div>

            {/* Vision */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-full bg-secondary/10 dark:bg-secondary/20 flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Our Vision</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                To be the leading provider of home care services in Tarrant County and surrounding areas, recognized for our excellence in care, innovation in service delivery, and unwavering commitment to improving the lives of those we serve. We envision a community where aging in place is not just possible, but comfortable, safe, and fulfilling.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Professional Home Care Services
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            At Aneeko Rapha Healthcare Services, we provide compassionate, personalized care services designed to help you or your loved ones live independently and comfortably at home.
          </p>
        </div>

        {/* Services Layout with Sidenav */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Services Sidenav */}
          <div className="lg:w-80 lg:flex-shrink-0">
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 lg:sticky lg:top-8">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Our Services</h3>
              <nav className="space-y-2">
                {services.map((service) => (
                  <button
                    key={service.id}
                    onClick={() => handleTabChange(service.id)}
                    className={cn(
                      "w-full flex items-center gap-4 p-4 rounded-lg transition-all duration-200 text-left",
                      activeTab === service.id
                        ? "bg-primary text-white shadow-md"
                        : "bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                    )}
                  >
                    <div
                      className={cn(
                        "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                        activeTab === service.id
                          ? "bg-white/20"
                          : "bg-white dark:bg-gray-700"
                      )}
                      style={{
                        backgroundColor: activeTab === service.id ? 'rgba(255,255,255,0.2)' : service.bgColor
                      }}
                    >
                      <service.icon
                        className="h-5 w-5"
                        style={{
                          color: activeTab === service.id ? 'white' : service.color
                        }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-sm leading-tight">{service.title}</h4>
                      <p className={cn(
                        "text-xs mt-1 leading-tight",
                        activeTab === service.id
                          ? "text-white/80"
                          : "text-gray-600 dark:text-gray-400"
                      )}>
                        {service.subtitle}
                      </p>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Service Content */}
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">

          {services.map((service) => (
            <TabsContent key={service.id} value={service.id} className="mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="border border-gray-200 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-900 rounded-xl overflow-hidden">
                  <CardContent className="p-0">
                    <div className="grid lg:grid-cols-2 gap-0">
                      {/* Service Image */}
                      <div className="relative h-64 lg:h-full min-h-[400px]">
                        <Image
                          src={service.image}
                          alt={`${service.title} - Aneeko Rapha Healthcare Services`}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 40vw"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                        <div className="absolute bottom-4 left-4 right-4">
                          <div className="flex items-center mb-2">
                            <div
                              className="w-12 h-12 rounded-full backdrop-blur-sm flex items-center justify-center mr-3"
                              style={{ backgroundColor: `${service.color}40` }}
                            >
                              <service.icon className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-white font-semibold text-lg">{service.title}</h3>
                              <p className="text-white/90 text-sm">{service.subtitle}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Service Info */}
                      <div className="p-8 lg:p-12">
                        <div className="flex items-center mb-6">
                          <div
                            className="w-16 h-16 rounded-full flex items-center justify-center mr-4"
                            style={{ backgroundColor: service.bgColor }}
                          >
                            <service.icon
                              className="h-8 w-8"
                              style={{ color: service.color }}
                            />
                          </div>
                          <div>
                            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">{service.title}</h2>
                            <p className="text-primary font-medium">{service.subtitle}</p>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">{service.description}</p>

                        <div className="space-y-8">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                              <CheckCircle className="h-5 w-5 text-primary" />
                              What We Provide:
                            </h3>
                            <ul className="space-y-2">
                              {service.benefits.map((benefit, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 rounded-full bg-primary mt-2 mr-3 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                              <Star className="h-5 w-5 text-secondary" />
                              Ideal For:
                            </h3>
                            <ul className="space-y-2">
                              {service.idealFor.map((item, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 rounded-full bg-secondary mt-2 mr-3 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{item}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                              Key Benefits:
                            </h3>
                            <ul className="space-y-2">
                              {service.keyBenefits.map((benefit, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 rounded-full bg-green-500 mt-2 mr-3 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                              Additional Services Available:
                            </h3>
                            <ul className="space-y-2">
                              {service.addOns.map((addon, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 rounded-full bg-orange-500 mt-2 mr-3 flex-shrink-0" />
                                  <span className="text-gray-600 dark:text-gray-300">{addon}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="bg-primary/5 dark:bg-primary/10 p-6 rounded-lg border border-primary/10">
                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                              Ready to Get Started?
                            </h4>
                            <p className="text-gray-600 dark:text-gray-300 mb-6">
                              Contact us today to learn more about our {service.title.toLowerCase()} and schedule your free consultation.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3">
                              <Link href="/consultation" className="flex-1">
                                <Button className="w-full bg-primary hover:bg-primary-dark text-white">
                                  Schedule Free Consultation
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </Button>
                              </Link>
                              <a href="tel:6822289234" className="flex-1">
                                <Button
                                  variant="outline"
                                  className="w-full border-primary text-primary hover:bg-primary hover:text-white"
                                >
                                  <Phone className="mr-2 h-4 w-4" />
                                  Call (*************
                                </Button>
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          ))}
            </Tabs>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
