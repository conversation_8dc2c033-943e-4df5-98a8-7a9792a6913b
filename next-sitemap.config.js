/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://araphahealthcare.com",
  generateRobotsTxt: false, // We have a custom robots.txt
  exclude: ["/admin/*", "/api/*"],
  generateIndexSitemap: false,
  changefreq: "weekly",
  priority: 0.7,
  sitemapSize: 5000,
  transform: async (config, path) => {
    // Custom priority for different pages
    const customPriority = {
      "/": 1.0,
      "/services": 0.9,
      "/contact": 0.8,
      "/about": 0.8,
      "/consultation": 0.8,
      "/testimonials": 0.7,
      "/blog": 0.7,
      "/careers": 0.6,
      "/careers/jobs": 0.5,
      "/resources": 0.6,
      "/refer": 0.5,
    }

    return {
      loc: path,
      changefreq: config.changefreq,
      priority: customPriority[path] || config.priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
    }
  },
}
