/**
 * SECOND half of the blog-post catalogue.
 * -------------------------------------------------
 * 1.  Import BlogPost type from part 1 for consistency
 * 2.  Export `blogPostsPart2`  (named)
 * 3.  Re-export it as the default export
 */

import type { BlogPost } from "./blog-data-part1"

export const blogPostsPart2: BlogPost[] = [
  {
    id: "future-home-care-trends",
    title: "Understanding the Different Types of Home Care Services",
    date: "October 26, 2023",
    author: "<PERSON>",
    readTime: "9 min read",
    excerpt:
      "Choosing the right type of home care service can be overwhelming. Here's a breakdown of the different options available.",
    content: "<p>Choosing the right type of home care service can be overwhelming...</p>",
    image: "/images/blog/home-care-types.jpg",
    icon: "HeartHandshake",
  },
  {
    id: "in-home-care-benefits",
    title: "The Benefits of In-Home Care for Seniors",
    date: "November 15, 2023",
    author: "<PERSON>",
    readTime: "9 min read",
    excerpt: "In-home care offers numerous benefits for seniors who wish to maintain independence.",
    content: "<p>In-home care offers numerous benefits for seniors who wish to remain independent...</p>",
    image: "/images/blog/in-home-care-benefits.jpg",
    icon: "Home",
  },
  {
    id: "home-care-agency-tips",
    title: "Tips for Choosing a Home Care Agency",
    date: "December 05, 2023",
    author: "Emily White",
    readTime: "9 min read",
    excerpt: "Selecting the right home care agency is crucial for ensuring quality care.",
    content: "<p>Selecting the right home care agency is crucial for ensuring quality care...</p>",
    image: "/images/blog/home-care-agency-tips.jpg",
    icon: "CheckSquare",
  },
]

export default blogPostsPart2
