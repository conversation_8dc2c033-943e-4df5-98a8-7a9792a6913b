"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Heart, Users, Shield, Home, Phone, Mail, ArrowRight, CheckCircle } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function RespiteCareClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Respite care benefits
  const benefits = [
    {
      title: "Caregiver Relief",
      description: "Provides essential breaks for family caregivers to rest, recharge, and attend to personal needs.",
      icon: Heart,
    },
    {
      title: "Flexible Scheduling",
      description: "Available for a few hours, overnight, or extended periods based on your family's needs.",
      icon: Clock,
    },
    {
      title: "Continuity of Care",
      description: "Maintains consistent, quality care for your loved one while you're away.",
      icon: Shield,
    },
    {
      title: "Professional Support",
      description: "Trained caregivers provide the same level of care and attention your loved one deserves.",
      icon: Users,
    },
  ]

  // When to consider respite care
  const whenToConsider = [
    "You need time for medical appointments or personal errands",
    "You're feeling overwhelmed or experiencing caregiver burnout",
    "You need to travel for work or family obligations",
    "You want to maintain social connections and personal relationships",
    "You need time for self-care and stress management",
    "You're recovering from illness or need medical care yourself",
    "You want to continue working or pursuing education",
    "You need regular breaks to maintain your physical and mental health"
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Respite Care Services"
        subtitle="Temporary relief for caregivers, continued quality care for loved ones"
        imageSrc="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/0f2ce-aa.png"
        imageAlt="Respite care services"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Respite Care
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Understanding Respite Care
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Respite care provides temporary relief for primary caregivers while ensuring your loved one continues to receive 
                the quality care and attention they need. It's an essential service that supports both caregivers and care recipients.
              </p>
            </div>
          </motion.section>

          {/* Benefits Grid */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Benefits of Respite Care
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-center">
                    <CardContent className="p-6 h-full flex flex-col">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                        <benefit.icon className="h-8 w-8 text-white" />
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3">
                        {benefit.title}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed flex-grow">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* When to Consider Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium">
                  When to Consider
                </Badge>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  When Should You Consider Respite Care?
                </h2>
                <div className="w-20 h-1 bg-secondary rounded-full"></div>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  Respite care is beneficial in many situations. Here are some common scenarios when families find respite care helpful:
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-4"
              >
                {whenToConsider.map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-secondary mt-0.5 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                      {item}
                    </span>
                  </div>
                ))}
              </motion.div>
            </div>
          </motion.section>

          {/* How It Works Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  How Respite Care Works
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                  Our respite care process is designed to be simple, flexible, and tailored to your family's specific needs.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center text-white font-bold text-lg">
                    1
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Initial Consultation</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    We assess your loved one's needs and your family's schedule to create a customized respite care plan.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-4 flex items-center justify-center text-white font-bold text-lg">
                    2
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Caregiver Matching</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    We match you with qualified caregivers who understand your loved one's specific needs and preferences.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 mx-auto mb-4 flex items-center justify-center text-white font-bold text-lg">
                    3
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Flexible Scheduling</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Schedule respite care when you need it - for a few hours, overnight, or extended periods.
                  </p>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Ready to Learn More About Respite Care?
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Contact us today to discuss how our respite care services can provide the support your family needs.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/care-tips">
                    <span>Back to Care Tips</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
