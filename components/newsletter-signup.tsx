"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, Send, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function NewsletterSignup() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter your email address.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch("/api/subscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (data.success) {
        setIsSubscribed(true)
        setEmail("")
        toast({
          title: "Successfully Subscribed!",
          description: "Thank you for subscribing to our newsletter.",
        })
      } else {
        throw new Error(data.error || "Subscription failed")
      }
    } catch (error) {
      toast({
        title: "Subscription Failed",
        description: "There was an error subscribing to our newsletter. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  return (
    <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950">
      <div className="px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]">
        <motion.div initial="hidden" animate="visible" variants={fadeIn}>
          <div className="text-center mb-12">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-primary/20 text-primary rounded-full text-sm font-medium mb-4">
                Stay Connected
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Subscribe to Our Newsletter
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              Stay updated with the latest care tips, health information, and news from Aneeko Rapha Healthcare Services.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <motion.div variants={fadeInUp}>
              <Card className="border border-gray-100 dark:border-gray-800 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden">
                <CardContent className="p-8">
                  {isSubscribed ? (
                    <div className="text-center">
                      <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto mb-4">
                        <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                      </div>
                      <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-2">
                        Thank You for Subscribing!
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        You'll receive our newsletter with helpful care tips and updates.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <div className="text-center mb-6">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mx-auto mb-4">
                          <Mail className="h-8 w-8 text-primary" />
                        </div>
                        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-2">
                          Join Our Newsletter
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300">
                          Get valuable care tips, health insights, and updates delivered to your inbox.
                        </p>
                      </div>

                      <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="flex flex-col sm:flex-row gap-3">
                          <Input
                            type="email"
                            placeholder="Enter your email address"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="flex-1 rounded-full border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary"
                            disabled={isLoading}
                          />
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-secondary hover:bg-secondary-dark text-gray-800 rounded-full px-8 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 whitespace-nowrap"
                          >
                            {isLoading ? (
                              <>
                                <div className="w-4 h-4 border-2 border-gray-800 border-t-transparent rounded-full animate-spin" />
                                Subscribing...
                              </>
                            ) : (
                              <>
                                <Send className="h-4 w-4" />
                                Subscribe
                              </>
                            )}
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                          We respect your privacy. Unsubscribe at any time.
                        </p>
                      </form>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
