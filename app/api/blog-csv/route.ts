import { NextResponse } from "next/server"
import { exportBlogsToCSV } from "@/app/blog/export-blogs-to-csv"

export async function GET() {
  try {
    // Generate CSV content for Journey of Care blog posts
    const csvContent = exportBlogsToCSV()

    // Create response with CSV content
    const response = new NextResponse(csvContent, {
      status: 200,
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": 'attachment; filename="journey-of-care-blog-posts.csv"',
      },
    })

    return response
  } catch (error) {
    console.error("Journey of Care - Error generating blog CSV:", error)
    return NextResponse.json({ error: "Failed to generate Journey of Care blog CSV export" }, { status: 500 })
  }
}
