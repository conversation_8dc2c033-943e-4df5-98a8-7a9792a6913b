import type { Metadata } from "next"
import SeniorCareClient from "./senior-care-client"

export const metadata: Metadata = {
  title: "Senior Care Tips | Aneeko Rapha Healthcare Services",
  description:
    "Comprehensive guide to senior care including health monitoring, safety tips, and quality of life improvements for elderly loved ones.",
  keywords: [
    "senior care tips",
    "elderly care",
    "aging in place",
    "senior health",
    "elderly safety",
    "senior wellness",
  ],
  alternates: {
    canonical: "/care-tips/senior-care",
  },
  openGraph: {
    title: "Senior Care Tips | Aneeko Rapha Healthcare Services",
    description:
      "Comprehensive guide to senior care including health monitoring, safety tips, and quality of life improvements for elderly loved ones.",
    url: "https://araphahealthcare.com/care-tips/senior-care",
  },
}

export default function SeniorCarePage() {
  return <SeniorCareClient />
}
