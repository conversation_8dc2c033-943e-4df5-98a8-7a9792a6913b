@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 1 65% 40%;
    --primary-foreground: 0 0% 98%;

    --secondary: 118 32% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 0 0% 46%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 1 65% 63%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 1 65% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 118 32% 35%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 0 0% 46%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 1 65% 68%;
  }

  html,
  body {
    overflow-x: hidden;
    position: relative;
    width: 100%;
    max-width: 100vw;
    font-family: var(--font-nunito-sans), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    font-size: 18px; /* Increased base font size for better readability */
    line-height: 1.6; /* Improved line height for easier reading */
  }

  /* Fix for double scrollbar issue */
  body {
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Ensure all containers respect boundaries */
  #__next,
  main,
  .container {
    overflow-x: hidden;
    max-width: 100%;
  }

  /* Typography styles - Increased sizes for better accessibility */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    line-height: 1.3; /* Slightly increased for better readability */
    letter-spacing: -0.01em;
  }

  h1 {
    font-size: 3rem; /* Increased from 2.5rem */
    font-weight: 700;
  }

  h2 {
    font-size: 2.5rem; /* Increased from 2rem */
    font-weight: 600;
  }

  h3 {
    font-size: 1.875rem; /* Increased from 1.5rem */
    font-weight: 600;
  }

  h4 {
    font-size: 1.5rem; /* Added explicit h4 styling */
    font-weight: 600;
  }

  h5 {
    font-size: 1.25rem; /* Added explicit h5 styling */
    font-weight: 600;
  }

  h6 {
    font-size: 1.125rem; /* Added explicit h6 styling */
    font-weight: 600;
  }

  p {
    line-height: 1.8; /* Increased from 1.7 for better readability */
    font-size: 1.125rem; /* Increased paragraph font size */
  }

  /* Base text size for better readability */
  body {
    font-size: 1.125rem; /* 18px base font size */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Enhanced mobile scrolling */
@media (max-width: 768px) {
  .custom-scrollbar {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  /* Ensure content doesn't get cut off on iOS */
  .custom-scrollbar {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Fix for iOS momentum scrolling */
.overflow-y-auto,
.overflow-y-scroll {
  -webkit-overflow-scrolling: touch;
}

/* Prevent body scroll when modal is open */
body.no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Blog content styling - Enhanced for accessibility */
.prose h1 {
  font-size: 2.75rem; /* Larger blog post titles */
  line-height: 1.2;
  margin-bottom: 1em;
}

.prose h2 {
  font-size: 2.25rem; /* Larger section headings */
  line-height: 1.3;
  margin-top: 1.5em;
  margin-bottom: 0.75em;
}

.prose h3 {
  color: hsl(240, 50%, 60%);
  font-size: 1.75rem; /* Increased from default */
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
  line-height: 1.3;
}

.dark .prose h3 {
  color: hsl(240, 60%, 75%);
}

.prose h4 {
  font-size: 1.375rem; /* Larger h4 for blog content */
  margin-top: 1.25em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.prose p {
  margin-bottom: 1.25em;
  line-height: 1.8; /* Increased line height */
  color: #1f2937 !important; /* Much darker text */
  font-size: 1.125rem; /* Larger paragraph text */
}

.dark .prose p {
  color: #e5e7eb !important; /* Lighter in dark mode */
}

.prose ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-bottom: 1.25em;
  font-size: 1.125rem; /* Larger list text */
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin-bottom: 1.25em;
  font-size: 1.125rem; /* Larger list text */
}

.prose li {
  margin-bottom: 0.5em;
  color: #1f2937 !important; /* Much darker text */
  line-height: 1.7; /* Better line height for list items */
}

.dark .prose li {
  color: #e5e7eb !important; /* Lighter in dark mode */
}

.prose a {
  color: hsl(240, 50%, 60%);
  text-decoration: underline;
  text-underline-offset: 2px;
}

.dark .prose a {
  color: hsl(240, 60%, 75%);
}

.prose a:hover {
  text-decoration: none;
}

.prose blockquote {
  border-left: 4px solid hsl(240, 50%, 60%);
  padding-left: 1em;
  font-style: italic;
  margin: 1.5em 0;
}

.dark .prose blockquote {
  border-left-color: hsl(240, 60%, 75%);
}

/* Fix for fixed position elements causing overflow */
.fixed {
  max-width: 100vw;
}

/* Fix for dropdown scrollbar issue */
body.dropdown-open {
  overflow-x: hidden;
  width: 100%;
}

/* Ensure dropdown doesn't cause horizontal scrolling */
.dropdown-container {
  max-width: 100vw;
  overflow-x: visible;
}

/* Hero section spacing */
.hero-section {
  margin-top: 140px;
}

@media (min-width: 768px) {
  .hero-section {
    margin-top: 150px;
  }
}

/* Modern button styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modern-button:hover::after {
  transform: translateX(0);
}

/* Enhanced accessibility styles for elderly users */
/* Button text sizing */
button, .btn {
  font-size: 1.125rem !important; /* 18px minimum for buttons */
  line-height: 1.5 !important;
  padding: 0.75rem 1.5rem !important; /* Larger click targets */
}

/* Navigation and menu text - smaller for header */
nav, .nav, .menu {
  font-size: 0.75rem !important; /* 12px for navigation to reduce header crowding */
}

/* Header specific overrides */
header nav {
  font-size: 0.75rem !important; /* 12px for header navigation */
}

header .text-lg {
  font-size: 0.75rem !important; /* Override large text in header */
}

header .text-base {
  font-size: 0.75rem !important; /* Override base text in header */
}

header .text-sm {
  font-size: 0.75rem !important; /* Override small text in header */
}

header .text-xs {
  font-size: 0.75rem !important; /* Override extra small text in header */
}

/* Form inputs and labels */
input, textarea, select, label {
  font-size: 1.125rem !important; /* 18px for form elements */
  line-height: 1.6 !important;
}

/* Card content */
.card, .card-content {
  font-size: 1.125rem !important;
  line-height: 1.7 !important;
}

/* Link text */
a {
  font-size: inherit;
  line-height: inherit;
  text-decoration-thickness: 2px; /* Thicker underlines for better visibility */
}

/* Badge and small text elements */
.badge, .text-xs, .text-sm {
  font-size: 1rem !important; /* Minimum 16px even for small text */
  line-height: 1.5 !important;
}

/* Darker text colors for better readability */
/* Override gray text colors to be much darker */
.text-gray-600 {
  color: #1f2937 !important; /* Much darker gray */
}

.text-gray-500 {
  color: #374151 !important; /* Much darker gray */
}

.text-gray-700 {
  color: #111827 !important; /* Nearly black */
}

/* Dark mode text colors */
.dark .text-gray-600 {
  color: #e5e7eb !important; /* Lighter in dark mode */
}

.dark .text-gray-500 {
  color: #d1d5db !important; /* Lighter in dark mode */
}

.dark .text-gray-700 {
  color: #f9fafb !important; /* Much lighter in dark mode */
}

/* Ensure minimum touch target sizes for mobile */
@media (max-width: 768px) {
  button, .btn, a {
    min-height: 44px; /* Minimum touch target size */
    min-width: 44px;
  }

  /* Larger text on mobile for better readability */
  body {
    font-size: 1.25rem; /* 20px on mobile */
  }

  p {
    font-size: 1.25rem !important;
    line-height: 1.8 !important;
  }

  h1 {
    font-size: 2.5rem !important;
  }

  h2 {
    font-size: 2rem !important;
  }

  h3 {
    font-size: 1.75rem !important;
  }
}

/* Additional global text color overrides */
/* Make all paragraph text darker by default */
p {
  color: #1f2937 !important;
}

.dark p {
  color: #e5e7eb !important;
}

/* Make span and div text darker */
span, div {
  color: inherit;
}

/* Ensure card text is dark */
.card p, .card span, .card div {
  color: #1f2937 !important;
}

.dark .card p, .dark .card span, .dark .card div {
  color: #e5e7eb !important;
}

/* Make list text darker */
li {
  color: #1f2937 !important;
}

.dark li {
  color: #e5e7eb !important;
}

/* Ensure headings are very dark */
h1, h2, h3, h4, h5, h6 {
  color: #111827 !important;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f9fafb !important;
}
