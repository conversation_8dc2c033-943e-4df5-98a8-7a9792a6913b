import { type NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"

export async function GET(request: NextRequest) {
  try {
    // Initialize Resend only when the route is called
    const resend = new Resend(process.env.RESEND_API_KEY)

    const { data, error } = await resend.emails.send({
      from: "Journey of Care <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: "Test Email from Journey of Care",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #7FB3D3;">Test Email</h1>
          <p>This is a test email from Journey of Care to verify email functionality.</p>
          <p>If you receive this email, the email service is working correctly.</p>
        </div>
      `,
    })

    if (error) {
      return NextResponse.json({ error }, { status: 400 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: "Failed to send test email" }, { status: 500 })
  }
}
