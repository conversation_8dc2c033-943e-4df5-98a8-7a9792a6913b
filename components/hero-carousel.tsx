"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

// Carousel slides data - Updated for Aneeko Rapha Healthcare Services
const slides = [
  {
    id: 1,
    image: "/images/caro1.jpg",
    title: "Your Trusted Home Care Agency",
    subtitle: "Exceptional, personalized home care for seniors and adults with disabilities in Tarrant County, TX",
    cta: "Schedule Your Free Consultation",
    ctaLink: "/consultation",
  },
  {
    id: 2,
    image: "/images/caro2.jpg",
    title: "Compassionate Care at Home",
    subtitle: "24-hour care and personalized support that nurtures mind, body, and spirit",
    cta: "Explore Our Services",
    ctaLink: "/services",
  },
  {
    id: 3,
    image: "/images/caro3.jpeg",
    title: "Quality Care You Can Trust",
    subtitle: "Dedicated caregivers selected for their warmth, compassion, and professional excellence",
    cta: "Call (*************",
    ctaLink: "tel:**********",
  },
]

export default function HeroCarousel() {
  const [current, setCurrent] = useState(0)
  const [autoplay, setAutoplay] = useState(true)

  // Autoplay functionality
  useEffect(() => {
    if (!autoplay) return

    const interval = setInterval(() => {
      setCurrent((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
    }, 5000)

    return () => clearInterval(interval)
  }, [autoplay])

  // Pause autoplay on hover
  const pauseAutoplay = () => setAutoplay(false)
  const resumeAutoplay = () => setAutoplay(true)

  // Navigation functions
  const goToNext = () => {
    setCurrent((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
    setAutoplay(false)
    setTimeout(() => setAutoplay(true), 5000)
  }

  const goToPrev = () => {
    setCurrent((prev) => (prev === 0 ? slides.length - 1 : prev - 1))
    setAutoplay(false)
    setTimeout(() => setAutoplay(true), 5000)
  }

  const goToSlide = (index: number) => {
    setCurrent(index)
    setAutoplay(false)
    setTimeout(() => setAutoplay(true), 5000)
  }

  // Animation variants
  const slideVariants = {
    enter: { opacity: 0, scale: 1.05 },
    center: { opacity: 1, scale: 1, transition: { duration: 0.7 } },
    exit: { opacity: 0, scale: 0.95, transition: { duration: 0.5 } },
  }

  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.7, delay: 0.3 } },
  }

  return (
    <div
      className="relative h-[90vh] min-h-[600px] max-h-[800px] w-full overflow-hidden mt-[80px]"
      onMouseEnter={pauseAutoplay}
      onMouseLeave={resumeAutoplay}
    >
      {/* Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={slides[current].id}
          initial="enter"
          animate="center"
          exit="exit"
          variants={slideVariants}
          className="absolute inset-0"
        >
          <Image
            src={slides[current].image || "/placeholder.svg"}
            alt={slides[current].title}
            fill
            priority
            className="object-cover"
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>

          {/* Content */}
          <div className="absolute inset-0 flex items-center justify-center px-4">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={textVariants}
              className="text-center max-w-4xl mx-auto"
            >
              <h1 className="text-3xl md:text-5xl lg:text-6xl font-light text-white mb-4 leading-tight drop-shadow-lg">
                {slides[current].title}
              </h1>
              <p className="text-lg md:text-xl text-white mb-8 drop-shadow-md">{slides[current].subtitle}</p>
              <Link href={slides[current].ctaLink}>
                <Button className="rounded-full bg-primary hover:bg-primary-dark text-white px-8 py-6 h-auto text-lg transition-all duration-300 hover:shadow-lg">
                  {slides[current].cta}
                </Button>
              </Link>
            </motion.div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows */}
      <button
        onClick={goToPrev}
        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 z-10"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>

      <button
        onClick={goToNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 z-10"
        aria-label="Next slide"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Dots Navigation */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === current ? "bg-white scale-125" : "bg-white/50 hover:bg-white/70"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          ></button>
        ))}
      </div>
    </div>
  )
}
