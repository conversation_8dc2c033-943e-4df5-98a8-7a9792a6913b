"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Calendar, Clock } from "lucide-react"

interface BlogPost {
  id: string
  title: string
  excerpt: string
  image: string
  date: string
  readTime: string
  category: string
}

export default function LatestBlogPosts() {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate fetching blog posts - replace with actual API call
    const fetchBlogPosts = async () => {
      try {
        // Featured blog posts using actual blog data
        const mockPosts: BlogPost[] = [
          {
            id: "healthy-aging-at-home",
            title: "Healthy Aging at Home: Practical Tips",
            excerpt:
              "Simple lifestyle changes can make aging in place safer and more enjoyable.",
            image:
              "/in1.jpg",
            date: "September 12, 2023",
            readTime: "7 min read",
            category: "Senior Care",
          },
          {
            id: "caregiver-burnout-signs",
            title: "Recognizing & Preventing Care-giver Burnout",
            excerpt:
              "Family caregivers often ignore their own well-being. Learn the warning signs.",
            image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1744967360/pexels-kampus-7551684_z6lvh6.jpg",
            date: "October 02, 2023",
            readTime: "6 min read",
            category: "Caregiver Support",
          },
          {
            id: "meal-prep-for-seniors",
            title: "Nutritious Meal Prep Ideas for Seniors",
            excerpt:
              "Quick, healthy recipes that support senior nutrition and independence.",
            image: "/in2.jpg",
            date: "October 18, 2023",
            readTime: "8 min read",
            category: "Nutrition",
          },
          {
            id: "future-home-care-trends",
            title: "Understanding the Different Types of Home Care Services",
            excerpt:
              "Choosing the right type of home care service can be overwhelming. Here's a breakdown of the different options available.",
            image:
              "/in4.jpg",
            date: "October 26, 2023",
            readTime: "9 min read",
            category: "Home Care",
          },
          {
            id: "in-home-care-benefits",
            title: "The Benefits of In-Home Care for Seniors",
            excerpt:
              "In-home care offers numerous benefits for seniors who wish to maintain independence.",
            image: "/in3.jpg",
            date: "November 15, 2023",
            readTime: "9 min read",
            category: "Senior Care",
          },
          {
            id: "home-care-agency-tips",
            title: "Tips for Choosing a Home Care Agency",
            excerpt:
              "Selecting the right home care agency is crucial for ensuring quality care.",
            image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1743512842/pexels-kampus-7551641_brs7jh.jpg",
            date: "December 05, 2023",
            readTime: "9 min read",
            category: "Home Care",
          },
        ]

        setBlogPosts(mockPosts)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching blog posts:", error)
        setIsLoading(false)
      }
    }

    fetchBlogPosts()
  }, [])

  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  }

  if (isLoading) {
    return (
      <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
        <div className="px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]">
          <div className="text-center mb-12">
            <div className="w-32 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto mb-4 animate-pulse"></div>
            <div className="w-64 h-8 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-4 animate-pulse"></div>
            <div className="w-96 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-xl h-80 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
      <div className="px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]">
        <motion.div initial="hidden" animate="visible" variants={fadeIn}>
          <div className="text-center mb-12">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-secondary/20 text-secondary rounded-full text-sm font-medium mb-4">
                Latest Articles
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Care Tips & Insights
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              Stay informed with our latest articles on home care, health tips, and family support strategies.
            </p>
          </div>

          <motion.div variants={staggerContainer} className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {blogPosts.map((post, index) => (
              <motion.div key={post.id} variants={fadeInUp}>
                <Link href={`/blog/${post.id}`} className="block h-full">
                  <Card className="h-full overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl group cursor-pointer">
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={post.image || "/placeholder.svg"}
                        alt={post.title}
                        width={400}
                        height={300}
                        className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-primary/90 text-white text-xs">{post.category}</Badge>
                      </div>
                    </div>

                    <CardContent className="p-6 flex flex-col h-full">
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>

                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-3 group-hover:text-primary dark:group-hover:text-primary-light transition-colors duration-300 line-clamp-2">
                        {post.title}
                      </h3>

                      <p className="text-gray-600 dark:text-gray-300 font-light mb-4 flex-grow line-clamp-3">
                        {post.excerpt}
                      </p>

                      <div className="mt-auto">
                        <div className="inline-flex items-center text-sm font-medium text-primary dark:text-primary-light group-hover:text-primary-dark dark:group-hover:text-primary transition-colors duration-300">
                          Read more
                          <ArrowRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>

          <div className="text-center">
            <Link href="/blog">
              <Button className="rounded-full bg-primary hover:bg-primary-dark text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                <span>View All Articles</span>
                <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
