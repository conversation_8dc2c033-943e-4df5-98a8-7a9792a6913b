"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, CalendarIcon, Send } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import Header from "@/components/header"
import { Gift } from "lucide-react"
import PageHero from "@/components/page-hero"
import Footer from "@/components/footer"

// Services data for checkbox selection - Updated for Aneeko Rapha Healthcare Services
const services = [
  {
    id: "personal-care",
    name: "Personal Care Services",
    description: "Assistance with daily living activities",
  },
  {
    id: "dementia-care",
    name: "Dementia Care",
    description: "Specialized care for memory-related conditions",
  },
  {
    id: "companion-care",
    name: "Companion Care",
    description: "Social interaction and emotional support",
  },
  {
    id: "homemaking",
    name: "Homemaking Services",
    description: "Light housekeeping and meal preparation",
  },
  {
    id: "respite-care",
    name: "Respite Care",
    description: "Temporary relief for family caregivers",
  },
  {
    id: "post-surgery-care",
    name: "Post-Surgery Care",
    description: "Recovery support after medical procedures",
  },
  {
    id: "24-hour-care",
    name: "24-Hour Care",
    description: "Round-the-clock supervision and care",
  },
]

export default function ConsultationClient() {
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const [date, setDate] = useState<Date>()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [smsConsent, setSmsConsent] = useState(false)

  const handleServiceChange = (serviceId: string, checked: boolean) => {
    if (checked) {
      setSelectedServices([...selectedServices, serviceId])
    } else {
      setSelectedServices(selectedServices.filter((id) => id !== serviceId))
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    const formData = new FormData(e.currentTarget)
    formData.append("services", selectedServices.join(", "))
    formData.append("preferredDate", date ? format(date, "PPP") : "")
    formData.append("smsConsent", smsConsent.toString())

    try {
      const response = await fetch("/api/consultation", {
        method: "POST",
        body: formData,
      })

      if (response.ok) {
        setIsSubmitted(true)
      } else {
        throw new Error("Failed to submit consultation request")
      }
    } catch (error) {
      console.error("Error submitting consultation request:", error)
      alert("There was an error submitting your request. Please try again or call us directly.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
        <Header />
        <main className="py-24 relative z-10">
          <div className={cn(containerClass)}>
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeIn}
              className="max-w-2xl mx-auto text-center"
            >
              <div className="mb-8">
                <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Gift className="h-10 w-10 text-green-600 dark:text-green-400" />
                </div>
                <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Thank You!
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                  Your consultation request has been submitted successfully. A member of our team from Aneeko Rapha Healthcare Services will contact you within 24 hours to schedule your free consultation.
                </p>
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    <strong>What happens next?</strong>
                  </p>
                  <ul className="text-left space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• We'll call you within 24 hours to confirm your consultation</li>
                    <li>• Our care coordinator will visit your home at your convenience</li>
                    <li>• We'll assess your needs and create a personalized care plan</li>
                    <li>• You'll receive a detailed proposal with no obligation</li>
                  </ul>
                </div>
                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild>
                    <Link href="/">Return to Home</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="tel:6822289234">Call (*************</a>
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Schedule Your Free Consultation"
        subtitle="Get personalized home care solutions for your loved one in Tarrant County, TX"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1753009413/pexels-olly-3781524_wyxspt.jpg"
        imageAlt="Healthcare consultation"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Free Consultation
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Schedule Your Free Home Care Assessment
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                Take the first step towards quality home care. Our experienced team will assess your needs and create a personalized care plan at no cost to you.
              </p>
            </div>

            <Card className="border border-gray-200 dark:border-gray-700 shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
              <CardContent className="p-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Personal Information */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                      Personal Information
                    </h2>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="firstName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          First Name *
                        </Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          type="text"
                          required
                          className="mt-1"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Last Name *
                        </Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          type="text"
                          required
                          className="mt-1"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Email Address *
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          className="mt-1"
                          placeholder="Enter your email address"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Phone Number *
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          required
                          className="mt-1"
                          placeholder="Enter your phone number"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Care Information */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                      Care Information
                    </h2>
                    <div>
                      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                        Who needs care? *
                      </Label>
                      <RadioGroup name="careRecipient" className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="myself" id="myself" />
                          <Label htmlFor="myself">Myself</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="spouse" id="spouse" />
                          <Label htmlFor="spouse">My spouse/partner</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="parent" id="parent" />
                          <Label htmlFor="parent">My parent</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="other-family" id="other-family" />
                          <Label htmlFor="other-family">Other family member</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="friend" id="friend" />
                          <Label htmlFor="friend">Friend</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  {/* Services Needed */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                      Services Needed
                    </h2>
                    <div className="grid md:grid-cols-2 gap-4">
                      {services.map((service) => (
                        <div key={service.id} className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                          <Checkbox
                            id={service.id}
                            checked={selectedServices.includes(service.id)}
                            onCheckedChange={(checked) => handleServiceChange(service.id, checked as boolean)}
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <Label htmlFor={service.id} className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                              {service.name}
                            </Label>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{service.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Preferred Date */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                      Preferred Consultation Date
                    </h2>
                    <div>
                      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                        When would you prefer to have your consultation?
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !date && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={date}
                            onSelect={setDate}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                      Additional Information
                    </h2>
                    <div>
                      <Label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Tell us more about your care needs
                      </Label>
                      <Textarea
                        id="message"
                        name="message"
                        rows={4}
                        className="mt-1"
                        placeholder="Please describe any specific care needs, medical conditions, or questions you have..."
                      />
                    </div>
                  </div>

                  {/* SMS Consent */}
                  <div className="flex items-start space-x-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <Checkbox
                      id="smsConsent"
                      checked={smsConsent}
                      onCheckedChange={(checked) => setSmsConsent(checked as boolean)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label htmlFor="smsConsent" className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                        By checking this box, I consent to receive text messages related to my consultation, appointment
                        scheduling, and care updates from Aneeko Rapha Healthcare Services. You can reply "STOP" at any time to opt
                        out. Message and data rates may apply. Message frequency may vary, text HELP to (*************
                        for assistance.
                      </Label>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-6">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white py-3 text-lg font-medium transition-all duration-300"
                    >
                      {isSubmitting ? (
                        "Submitting..."
                      ) : (
                        <>
                          <Send className="mr-2 h-5 w-5" />
                          Schedule My Free Consultation
                        </>
                      )}
                    </Button>
                    <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                      By submitting this form, you agree to be contacted by Aneeko Rapha Healthcare Services regarding your consultation request.
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
