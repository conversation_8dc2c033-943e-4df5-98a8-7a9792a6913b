// Using Brevo's API directly instead of SMTP

export interface EmailData {
  to: string | { email: string; name?: string }[]
  subject: string
  htmlContent: string
  sender?: { name: string; email: string }
  replyTo?: { name: string; email: string }
  params?: Record<string, any>
  attachment?: Array<{
    content: string
    name: string
    type: string
  }>
}

export async function sendEmail(data: EmailData): Promise<boolean> {
  const { to, subject, htmlContent, sender, replyTo, params, attachment } = data

  // Check if API key is available
  const apiKey = process.env.BREVO_API_KEY
  if (!apiKey) {
    console.error("BREVO_API_KEY is not defined in environment variables")
    return false
  }

  // Format recipients if to is a string
  const recipients = typeof to === "string" ? [{ email: to }] : to

  const payload: any = {
    sender: sender || {
      name: "<PERSON><PERSON><PERSON>'s Angel Healthcare",
      email: process.env.DEFAULT_FROM_EMAIL || "<EMAIL>",
    },
    to: recipients,
    subject,
    htmlContent,
    replyTo: replyTo || {
      name: "<PERSON><PERSON><PERSON>'s Angel Healthcare",
      email: process.env.DEFAULT_REPLY_TO || "<EMAIL>",
    },
    params: params || {},
  }

  // Add attachment if provided
  if (attachment && attachment.length > 0) {
    payload.attachment = attachment
  }

  // Debug log to see what's being sent
  console.log(
    "Sending email with payload:",
    JSON.stringify({ ...payload, attachment: attachment ? "[ATTACHMENT_DATA]" : undefined }, null, 2),
  )
  console.log("Using API key:", apiKey ? "API key is present" : "API key is missing")

  try {
    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        "api-key": apiKey,
      },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Brevo API error:", errorData)
      console.error("Response status:", response.status)
      console.error("Response headers:", Object.fromEntries([...response.headers.entries()]))
      return false
    }

    console.log("Email sent successfully via Brevo API")
    return true
  } catch (error) {
    console.error("Error sending email via Brevo API:", error)
    return false
  }
}
