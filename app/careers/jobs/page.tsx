import type { Metadata } from "next"
import JobsClient from "./jobs-client"

export const metadata: Metadata = {
  title: "Current Job Openings | Aneeko Rapha Healthcare Services - Tarrant County, TX",
  description:
    "View current job openings at Aneeko Rapha Healthcare Services in Tarrant County, TX. Join our team of compassionate caregivers and make a difference in people's lives.",
  keywords: [
    "home health aide jobs Tarrant County TX",
    "personal care assistant jobs",
    "caregiver jobs Tarrant County",
    "healthcare jobs Texas",
    "Aneeko Rapha careers",
    "home care employment",
    "certified nursing assistant jobs",
  ],
  alternates: {
    canonical: "/careers/jobs",
  },
  openGraph: {
    title: "Current Job Openings | Aneeko Rapha Healthcare Services - Tarrant County, TX",
    description:
      "View current job openings at Aneeko Rapha Healthcare Services in Tarrant County, TX. Join our team of compassionate caregivers and make a difference in people's lives.",
    url: "https://araphahealthcare.com/careers/jobs",
  },
}

export default function JobsPage() {
  return <JobsClient />
}
