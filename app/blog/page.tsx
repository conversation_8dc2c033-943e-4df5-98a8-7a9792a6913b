"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Search,
  Calendar,
  Clock,
  ChevronRight,
  Activity,
  Brain,
  Heart,
  UserRound,
  Dumbbell,
  Laptop,
  Smile,
  AlertCircle,
  ListChecks,
  Lightbulb,
  Shield,
  ClipboardList,
  HeartHandshake,
  Home,
  Building2,
  CheckSquare,
  Scale,
  Stethoscope,
  Users,
  Wallet,
  MessageSquare,
} from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { blogPosts } from "./blog-data"
import PageHero from "@/components/page-hero"

// Map of icon names to Lucide React components
const iconMap = {
  Activity,
  Brain,
  Heart,
  UserRound,
  <PERSON><PERSON><PERSON>,
  Lapt<PERSON>,
  Smile,
  AlertCircle,
  ListChecks,
  Lightbulb,
  Shield,
  ClipboardList,
  HeartHandshake,
  Home,
  Building2,
  CheckSquare,
  Scale,
  Stethoscope,
  Users,
  Wallet,
  MessageSquare,
}

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState("")

  // Filter blog posts based on search query
  const filteredPosts = blogPosts.filter(
    (post) =>
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Function to get icon component
  const getIcon = (iconName) => {
    const IconComponent = iconMap[iconName] || Activity
    return <IconComponent className="h-full w-full text-primary dark:text-primary-light opacity-80" />
  }

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Our Blog"
        subtitle="Insights and resources for better home care"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747926076/pexels-pixabay-262508_l6h1r0.jpg"
        imageAlt="Person writing in a journal"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <div className="max-w-5xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
              Aneeko Rapha Healthcare Blog
            </h1>

            <p className="text-gray-600 dark:text-gray-300 mb-12 max-w-3xl">
              Stay informed with the latest news, insights, and tips about home care and wellness from Aneeko Rapha Healthcare
             . Our blog is dedicated to helping you and your loved ones in Tarrant County, TX and surrounding areas live
              healthier, more comfortable lives at home.
            </p>

            {/* Search Bar */}
            <div className="mb-12">
              <div className="relative max-w-md mx-auto md:mx-0">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  className="pl-10 pr-4 py-2 border-gray-200 dark:border-gray-700 rounded-full focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Blog Posts */}
            <div className="grid md:grid-cols-2 gap-8">
              {filteredPosts.length > 0 ? (
                filteredPosts.map((post) => (
                  <div key={post.id} className="transition-all duration-300 hover:-translate-y-1">
                    <Card className="h-full overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-lg transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                      <CardContent className="p-0 flex flex-col h-full">
                        <div className="relative h-48 w-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center p-8">
                          <div className="w-24 h-24 text-primary">{getIcon(post.icon)}</div>
                        </div>
                        <div className="p-6 flex flex-col flex-grow">
                          <div className="flex items-center gap-2 mb-3">
                            <Badge className="bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light hover:bg-primary/10 hover:text-primary">
                              Home Care
                            </Badge>
                          </div>
                          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3 line-clamp-2">
                            {post.title}
                          </h2>
                          <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 flex-grow">{post.excerpt}</p>
                          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              <span>{post.date}</span>
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              <span>{post.readTime}</span>
                            </div>
                          </div>
                          <Link href={`/blog/${post.id}`} className="mt-auto">
                            <Button
                              variant="outline"
                              className="w-full justify-between border-gray-200 dark:border-gray-700 hover:border-primary dark:hover:border-primary-light hover:text-primary dark:hover:text-primary-light group bg-transparent"
                            >
                              Read More
                              <ChevronRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))
              ) : (
                <div className="col-span-2 text-center py-12">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">No articles found matching your search.</p>
                  <Button
                    variant="outline"
                    onClick={() => setSearchQuery("")}
                    className="border-primary dark:border-primary-light text-primary dark:text-primary-light"
                  >
                    Clear Search
                  </Button>
                </div>
              )}
            </div>

            {/* Contact CTA */}
            <div className="mt-16 text-center bg-primary/10 dark:bg-primary/20 p-8 rounded-xl border border-primary/10 dark:border-primary-light/10">
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-4">
                Need More Information About Home Care Services?
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                Contact Aneeko Rapha Healthcare today to learn more about our comprehensive home care services in Tarrant County, TX
                and surrounding areas.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/consultation">
                  <Button className="bg-primary hover:bg-primary-dark text-white px-8">Schedule Free Assessment</Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" className="border-gray-200 dark:border-gray-700 px-8 bg-transparent">
                    Contact Us
                  </Button>
                </Link>
              </div>
              <div className="mt-6 text-sm text-gray-600 dark:text-gray-300">
                <p>
                  <strong>Phone:</strong>{" "}
                  <a href="tel:+18324460705" className="text-primary hover:underline">
                    (*************
                  </a>
                </p>
                <p>
                  <strong>Email:</strong>{" "}
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
