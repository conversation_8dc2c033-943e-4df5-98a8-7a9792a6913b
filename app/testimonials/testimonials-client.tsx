"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Quote, ArrowRight, Phone, Mail, Target, Eye } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function TestimonialsClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Testimonials data
  const testimonials = [
    {
      quote: "Thank you so much Aneeko Rapha Healthcare Services for all the care you've been providing for my Mom. We've had other attendants before but since your attendant started working with her, we can see all the positive changes. Keep up the good work.",
      author: "<PERSON>",
      image: "https://new.mamassizzler.co.nz/wp-content/uploads/2024/04/quote.png",
    },
    {
      quote: "Having an attendant from Aneek<PERSON> was the best decision we made when our father's health took a turn for the worse. We all noticed that the attendant truly cared about him as an individual. And we know this helped with his quick recovery.",
      author: "Anna S.",
      image: "https://new.mamassizzler.co.nz/wp-content/uploads/2024/04/quote.png",
    },
    {
      quote: "Aneeko Rapha is great company. After my surgery, they were a life-saver as the attendant they provided went the extra mile to make sure I was cared for and comfortable in my home. I believe that my rapid recovery was a result of this care. If you are still hesitant about which Home Health Company to go with, I say, go with Aneeko Rapha!",
      author: "Kevin Y.",
      image: "https://new.mamassizzler.co.nz/wp-content/uploads/2024/04/quote.png",
    },
    {
      quote: "Thank you! Sherry was really good, she was patient, professional, personable and efficient…",
      author: "Mrs. R.",
      image: "https://new.mamassizzler.co.nz/wp-content/uploads/2024/04/quote.png",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Client Testimonials"
        subtitle="What clients are saying about Aneeko Rapha Healthcare Services"
        imageSrc="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
        imageAlt="Happy clients and families"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Client Stories
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                What Clients Are Saying About Aneeko Rapha Healthcare Services
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Don't just take our word for it. Read what families are saying about their experience with our compassionate home care services.
              </p>
            </div>
          </motion.section>

          {/* Testimonials Grid */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid md:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                    <CardContent className="p-8 h-full flex flex-col">
                      <div className="flex items-center mb-6">
                        <Quote className="h-8 w-8 text-primary dark:text-primary-light" />
                      </div>
                      
                      <blockquote className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6 flex-grow italic">
                        "{testimonial.quote}"
                      </blockquote>
                      
                      <div className="flex items-center gap-3 mt-auto">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-primary-dark flex items-center justify-center">
                          <span className="text-white font-semibold text-lg">
                            {testimonial.author.split(' ')[0][0]}
                          </span>
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800 dark:text-gray-100">{testimonial.author}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Verified Client</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Parallax Section */}
          <section className="relative py-32 overflow-hidden mb-20">
            <div className="absolute inset-0 bg-gradient-to-r from-secondary/90 to-primary/90 dark:from-secondary/80 dark:to-primary/80"></div>
            <div
              className="absolute inset-0 bg-cover bg-center bg-fixed"
              style={{
                backgroundImage: "url('https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/0f2ce-aa.png')"
              }}
            ></div>
            <div className="relative z-10">
              <div className={cn(containerClass)}>
                <div className="text-center text-white max-w-4xl mx-auto">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                  >
                    <h2 className="text-4xl md:text-5xl font-light mb-6">
                      Trusted by Families Everywhere
                    </h2>
                    <p className="text-xl md:text-2xl font-light opacity-90 leading-relaxed">
                      Our commitment to excellence has earned us the trust of countless families who rely on us for compassionate, professional home care services.
                    </p>
                  </motion.div>
                </div>
              </div>
            </div>
          </section>

          {/* Mission Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium">
                  Our Mission
                </Badge>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Exceptional Healthcare with Integrity
                </h2>
                <div className="w-20 h-1 bg-primary rounded-full"></div>
                <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    At Aneeko Rapha Health Care Services, our mission is to provide exceptional healthcare services with integrity, empathy, and excellence. We strive to deliver comprehensive and personalized care that meets the diverse needs of our patients.
                  </p>
                  <p>
                    Through continuous innovation, collaboration, and a commitment to excellence, we aim to improve health outcomes and empower individuals to live healthier, happier lives.
                  </p>
                </div>
                <div className="flex items-center gap-4 pt-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-primary-dark flex items-center justify-center">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-gray-100">Our Goal</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Empowering independence through quality care</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
                    alt="Healthcare mission"
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  <div className="absolute bottom-6 left-6 text-white">
                    <p className="text-lg font-medium">Exceptional Healthcare Services</p>
                    <p className="text-sm opacity-90">Delivered with integrity and compassion</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Vision Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="relative order-2 lg:order-1"
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/0f2ce-aa.png"
                    alt="Healthcare vision"
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  <div className="absolute bottom-6 left-6 text-white">
                    <p className="text-lg font-medium">Leading Healthcare Provider</p>
                    <p className="text-sm opacity-90">Compassionate care for all</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-6 order-1 lg:order-2"
              >
                <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium">
                  Our Vision
                </Badge>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Leading Provider of Compassionate Care
                </h2>
                <div className="w-20 h-1 bg-secondary rounded-full"></div>
                <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    To become the leading provider of accessible, innovative, and compassionate healthcare solutions, enhancing the well-being of individuals and communities.
                  </p>
                  <p>
                    We envision a future where quality healthcare is accessible to all, delivered with innovation and compassion in the comfort of one's own home.
                  </p>
                </div>
                <div className="flex items-center gap-4 pt-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-secondary to-secondary-dark flex items-center justify-center">
                    <Eye className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-gray-100">Our Vision</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Accessible, innovative healthcare for all</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Schedule a Service Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Schedule a Service
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Ready to experience the exceptional care that our clients are talking about? Contact Aneeko Rapha Healthcare Services today to schedule your free consultation.
              </p>
              
              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6 flex items-center justify-center">
                    <Phone className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Call for Immediate Service</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Speak directly with our care coordinators to discuss your needs and schedule services.
                  </p>
                  <a href="tel:**********" className="text-2xl font-bold text-primary hover:text-primary-dark transition-colors">
                    (*************
                  </a>
                </div>
                
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-6 flex items-center justify-center">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Email Consultation</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Send us your questions and we'll respond with detailed information about our services.
                  </p>
                  <a href="mailto:<EMAIL>" className="text-lg font-semibold text-secondary hover:text-secondary-dark transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/services">
                    <span>View All Services</span>
                  </Link>
                </Button>
                <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100 hover:text-gray-900 px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/careers">
                    <span>Join Our Team</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
