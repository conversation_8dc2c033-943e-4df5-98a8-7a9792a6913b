"use client"

import { useEffect, useState, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import Head from "next/head"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  Heart,
  Users,
  Shield,
  ChevronRight,
  Phone,
  Mail,
  Send,
  Star,
  Quote,
  ArrowRight,
  Clock,
  Award,
  Share2,
  ThumbsUp,
  Gift,
  HeartHandshake,
  ClipboardList,
  Home,
  MapPin,
  Brain,
  Activity
} from "lucide-react"
import { useInView } from "react-intersection-observer"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import Header from "@/components/header"
import HeroCarousel from "@/components/hero-carousel"
import Footer from "@/components/footer"

import LatestBlogPosts from "@/components/latest-blog-posts"

function Tiktok({ className = "" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M33.6,14.5c-2-1.4-3.3-3.6-3.4-6.2h-5.7v23.6c0,2.4-1.9,4.3-4.3,4.3S16,34.3,16,31.9s1.9-4.3,4.3-4.3c0.6,0,1.1,0.1,1.6,0.3v-6c-0.5-0.1-1-0.1-1.6-0.1c-5.7,0-10.3,4.6-10.3,10.3S14.7,42,20.3,42s10.3-4.6,10.3-10.3v-9.6c1.8,1.3,4.1,2.1,6.5,2.1V18C36.4,18,34.8,16.5,33.6,14.5z" />
    </svg>
  )
}

function YouTubeIcon({ className = "h-5 w-5" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
      <path
        fill="#FF0000"
        d="M43.6 14.2s-.4-3-1.6-4.3c-1.5-1.6-3.2-1.6-4-1.7-5.6-.4-14.1-.4-14.1-.4s-8.5 0-14.1.4c-.8.1-2.5.1-4 1.7C4.6 11.2 4.2 14.2 4.2 14.2S3.8 17.6 3.8 21v5.9c0 3.4.4 6.8.4 6.8s.4 3 1.6 4.3c1.5 1.6 3.5 1.5 4.4 1.7 3.2.3 13.5.4 13.5.4s8.5 0 14.1-.4c.8-.1 2.5-.1 4-1.7 1.2-1.3 1.6-4.3 1.6-4.3s.4-3.4.4-6.8V21c0-3.4-.4-6.8-.4-6.8z"
      />
      <path fill="#FFF" d="M20 31.5l12.2-7.5L20 16.5z" />
    </svg>
  )
}

function PinterestIcon({ className = "h-5 w-5" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
      <circle cx="24" cy="24" r="22" fill="#E60023" />
      <path
        fill="#fff"
        d="M25.4 28.1c-.9 4.7-2 9.2-5 12.2-1-.7-1.3-2.1-1.5-3.2-.3-1.3-.6-2.6-.8-4-.2-1.1-.5-2.2-.8-3.3-.3-1.3-.1-2.2.6-3.1 1.3-1.6 1.8-3.3 1.9-5.4 0-3.2 2.2-5.6 5.4-5.6 3.1 0 4.6 2.3 4.6 5.1 0 3.1-1.4 5.7-3.4 5.7-1.1 0-1.9-.9-1.6-2.1.3-1.2.9-2.4.9-3.6 0-.8-.4-1.6-1.3-1.6-1 0-1.8 1-1.8 2.4 0 1 .3 1.7.3 1.7s-1 4.2-1.2 4.9c-.3 1.4-.2 3.3-.1 4.6.1.6.5.9 1 .5 1.2-1.4 2.2-3.5 2.5-5.2.2-.8 1.1-3.9 1.1-3.9.6 1 2.1 1.9 3.7 1.9 4.9 0 6.9-4.3 6.9-8 0-3.4-2.9-6.7-7.7-6.7-5.8.1-9.3 4.3-9.3 8.9 0 1.6.5 3 1.3 3.9.1.1.1.1.1.2-.1.4-.3 1.4-.3 1.6-.1.2-.2.3-.4.2-1.4-.7-2.2-2.7-2.2-4.3 0-3.6 2.8-7.8 8.4-7.8 4.4 0 7.8 3.1 7.8 7.3 0 4.4-2.7 8-6.5 8-1.3 0-2.6-.7-3-1.5l-.9 3.4z"
      />
    </svg>
  )
}

export default function HomePage() {
  const [scrolled, setScrolled] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [videoPlaying, setVideoPlaying] = useState(true)
  const [videoMuted, setVideoMuted] = useState(true)
  const { setTheme, theme } = useTheme()
  const menuRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Make sure the component is mounted before accessing theme
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close menu when clicking outside
  useEffect(() => {
    if (!mobileMenuOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.body.style.overflow = "hidden"

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = ""
    }
  }, [mobileMenuOpen])

  // Video controls
  const toggleVideoPlay = () => {
    if (videoRef.current) {
      if (videoPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setVideoPlaying(!videoPlaying)
    }
  }

  const toggleVideoMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoMuted
      setVideoMuted(!videoMuted)
    }
  }

  // Simplified animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  // Simplified intersection observer hooks
  const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: servicesRef, inView: servicesInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: referRef, inView: referInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: betterChoiceRef, inView: betterChoiceInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: aboutRef, inView: aboutInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: missionRef, inView: missionInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: testimonialsRef, inView: testimonialsInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: contactRef, inView: contactInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: faqRef, inView: faqInView } = useInView({ triggerOnce: true, threshold: 0.1 })

  // Custom container class with increased margins on desktop
  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Updated services data for Aneeko Rapha Healthcare Services - Complete service offerings
  const services = [
    {
      id: "personal-care",
      title: "Personal Care Services",
      description:
        "Compassionate assistance with daily living activities including bathing, dressing, grooming, and mobility support. Our caregivers provide respectful, dignified care tailored to individual needs and preferences.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/5b6c7-image.jpeg",
      color: "#e02b2a",
      icon: Heart,
    },
    {
      id: "dementia-care",
      title: "Dementia Care",
      description:
        "Specialized memory care services for individuals with Alzheimer's, dementia, and other cognitive conditions. Our trained caregivers provide patient, understanding support in familiar surroundings.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/4c561-alzheimers-800.jpg",
      color: "#58a957",
      icon: Shield,
    },
    {
      id: "companion-care",
      title: "Companion Care",
      description:
        "Social and emotional support to combat isolation and loneliness. Our companions engage in meaningful conversations, activities, and provide the friendship that enriches daily life.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/801a3-companionship-picture.jpeg",
      color: "#e02b2a",
      icon: Users,
    },
    {
      id: "homemaking",
      title: "Homemaking Services",
      description:
        "Light housekeeping, meal preparation, laundry, and home organization services. We help maintain a clean, safe, and comfortable living environment for our clients.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/7e0ab-homemaking.jpg",
      color: "#58a957",
      icon: Home,
    },
    {
      id: "respite-care",
      title: "Respite Care",
      description:
        "Temporary relief for family caregivers who need a break. Our respite care services provide peace of mind knowing your loved one is in capable, caring hands.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/e711c-**********.jpg",
      color: "#e02b2a",
      icon: HeartHandshake,
    },
    {
      id: "post-surgery-care",
      title: "Post-Surgery Care",
      description:
        "Specialized recovery support following medical procedures. Our caregivers assist with medication reminders, mobility support, and ensure a safe, comfortable recovery at home.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/7ac60-supported-living-confidence-care.jpg",
      color: "#58a957",
      icon: Award,
    },
    {
      id: "24-hour-care",
      title: "24-Hour Care",
      description:
        "Round-the-clock supervision and care for clients who need continuous support. Multiple caregivers rotate to ensure someone is always awake and available to provide assistance.",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/ad9fd-front-view-elder-women-wearing-medical-masks-home-while-doing-activities.jpg",
      color: "#e02b2a",
      icon: Clock,
    },
  ]

  // Contact information - Updated for Aneeko Rapha Healthcare Services
  const contactInfo = [
    {
      label: "Phone",
      value: "(*************",
      icon: Phone,
    },
    {
      label: "Email",
      value: "<EMAIL>",
      icon: Mail,
    },
    {
      label: "Service Area",
      value: "Tarrant County, TX",
      icon: MapPin,
    },
  ]

  // Testimonials data - Aneeko Rapha Healthcare Services
  const testimonials = [
    {
      quote:
        "Thank you so much Aneeko Rapha Healthcare Services for all the care you've been providing for my Mom. We've had other attendants before but since your attendant started working with her, we can see all the positive changes. Keep up the good work.",
      author: "John R.",
      location: "Tarrant County, TX",
    },
    {
      quote:
        "Having an attendant from Aneeko Rapha was the best decision we made when our father's health took a turn for the worse. We all noticed that the attendant truly cared about him as an individual. And we know this helped with his quick recovery.",
      author: "Anna S.",
      location: "Tarrant County, TX",
    },
    {
      quote:
        "Aneeko Rapha is great company. After my surgery, they were a life-saver as the attendant they provided went the extra mile to make sure I was cared for and comfortable in my home. I believe that my rapid recovery was a result of this care. If you are still hesitant about which Home Health Company to go with, I say, go with Aneeko Rapha!",
      author: "Kevin Y.",
      location: "Tarrant County, TX",
    },
    {
      quote:
        "Thank you! Sherry was really good, she was patient, professional, personable and efficient. The care provided exceeded our expectations.",
      author: "Mrs. R.",
      location: "Tarrant County, TX",
    },
  ]

  // FAQ data - Updated for Journey of Care
  const faqs = [
    {
      question: "What home care services does Aneeko Rapha Healthcare Services offer?",
      answer:
        "We offer comprehensive home care services including personal care assistance with daily living activities, dementia care for memory-related conditions, companion care for emotional support, homemaking services, respite care for family caregivers, post-surgery care for recovery support, and 24-hour care for continuous supervision. All our services are tailored to meet the unique needs of each client throughout Tarrant County, TX.",
    },
    {
      question: "How do I know if my loved one needs home care services?",
      answer:
        "Signs that indicate your loved one might need home care include difficulty with daily activities (bathing, dressing, meal preparation), medication management issues, mobility challenges, social isolation, or a recent hospital stay. We offer free in-home assessments to evaluate needs and determine the most appropriate care plan.",
    },
    {
      question: "Are your caregivers licensed and insured?",
      answer:
        "Yes, all our caregivers are certified, insured, and undergo comprehensive background checks. Our team receives ongoing training to ensure they provide the highest quality of care. We are fully compliant with all state and federal regulations governing home care providers in Texas.",
    },
    {
      question: "What makes Aneeko Rapha Healthcare different?",
      answer:
        "We are dedicated to providing trustworthy, heartfelt care with a reputation built on reliability. Our personalized approach ensures that each care plan is tailored to the individual's specific needs and preferences. We treat every client with compassion and respect, offering steady hands and open hearts every day.",
    },
    {
      question: "What are your rates and do you accept insurance?",
      answer:
        "Our rates vary depending on the type and level of care needed. We accept long-term care insurance, private pay, and offer flexible payment arrangements. We work with families to explore all available payment options and insurance benefits to ensure you receive the care you need. Contact us at (************* for a free consultation to discuss pricing and payment options.",
    },
    {
      question: "How quickly can home care services start?",
      answer:
        "In most cases, we can initiate services within 24-48 hours of an initial assessment. For urgent situations, we strive to provide care as quickly as possible. The timeline depends on the complexity of care needed and scheduling availability.",
    },
    {
      question: "What areas do you serve?",
      answer:
        "We proudly serve Tarrant County, TX including Tarrant County, Arlington, Grand Prairie, Euless, Bedford, Hurst, Keller, Southlake, Grapevine, Colleyville, and surrounding communities. Please contact us at (************* for specific information about service availability in your location.",
    },
  ]

  // Core values data - Aneeko Rapha Healthcare Services
  const coreValues = [
    {
      icon: Heart,
      title: "Integrity & Empathy",
      description: "We provide exceptional healthcare services with integrity, empathy, and genuine care for every client.",
    },
    {
      icon: Shield,
      title: "Quality Assurance",
      description: "Quality is at the heart of everything we do. We maintain the highest standards of care and safety.",
    },
    {
      icon: Award,
      title: "Personalized Care",
      description: "We deliver comprehensive and personalized care that meets the diverse needs of our clients.",
    },
    {
      icon: Users,
      title: "Holistic Support",
      description: "Our approach nurtures the mind, body, and spirit, allowing clients to live with dignity and purpose.",
    },
  ]

  // Trust section benefits
  const trustBenefits = [
    {
      icon: HeartHandshake,
      title: "Trustworthy and Compassionate Caregivers",
      description: "Certified caregivers who provide reliable, heartfelt support with steady hands and open hearts.",
    },
    {
      icon: ClipboardList,
      title: "Customized Care Plans",
      description: "Personalized care plans tailored to each individual's unique needs and preferences.",
    },
    {
      icon: Clock,
      title: "24/7 Availability for Emergencies",
      description: "Round-the-clock availability to ensure peace of mind and support when you need it most.",
    },
    {
      icon: Award,
      title: "Serving with Dedication",
      description: "Committed to improving quality of life with care, professionalism, and dedication.",
    },
  ]

  return (
    <>
      <Head>
        <title>Aneeko Rapha Healthcare Services | Your Trusted Home Care Agency in Tarrant County, TX</title>
        <meta
          name="description"
          content="Aneeko Rapha Healthcare Services provides exceptional, personalized home care for seniors and adults with disabilities in Tarrant County, TX. 24-hour care, dementia care, personal care, and more."
        />
        <meta
          name="keywords"
          content="home care Tarrant County TX, personal care services, dementia care, companion care, respite care, 24 hour care, post surgery care, homemaking services, Tarrant County home care agency"
        />
        <link rel="canonical" href="https://araphahealthcare.com/" />
      </Head>

      <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-x-hidden overflow-y-hidden max-w-[100vw] transition-colors duration-300">
        {/* Gradient Orbs */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none z-0 max-w-[100vw]">
          <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
          <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
        </div>

        <Header />

        {/* Hero Carousel */}
        <HeroCarousel />

        {/* Main Content with proper H1 */}
        <main>
          {/* Welcome Banner */}
          <section className="py-12 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Your Trusted Home Care Agency
                </h1>
                <p className="text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                  Exceptional Healthcare Services with Integrity, Empathy, and Excellence
                </p>
                <div className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6 space-y-4">
                  <p>
                    At Aneeko Rapha Healthcare Services, we understand the importance of independence and comfort for seniors and adults with disabilities in need of assistance with daily activities. Our dedicated caregivers are here to provide personalized care and assistance, allowing your loved ones to thrive in the familiar surroundings of their own home.
                  </p>
                  <p>
                    We offer a range of home care services for seniors and adults with disabilities, and we strive to bring the best possible care to the comfort of your home. Our approach to care goes beyond simply meeting physical needs — we believe in providing holistic support that nurtures the mind, body, and spirit.
                  </p>
                  <p>
                    Serving Tarrant County, TX and surrounding areas, we are committed to maintaining the highest standards of care and continuously seeking ways to improve and enhance our services. At Aneeko Rapha Healthcare Services, your peace of mind is our promise of care.
                  </p>
                </div>

                <div className="mt-8">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                      <span>Schedule Your Free Consultation</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                  <p className="text-gray-600 dark:text-gray-300 mt-4">
                    Call us at{" "}
                    <a href="tel:+1**********" className="text-primary hover:underline font-medium">
                      (*************
                    </a>{" "}
                    or contact us at{" "}
                    <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium">
                      <EMAIL>
                    </a>{" "}
                    to get started.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Specialized Care Services Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                    Specialized Care
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Expert Specialized Care Services
                </h2>
                <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-8"></div>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  Our specialized care services are designed for clients with unique needs requiring expert attention. From dementia and Alzheimer's care to post-surgery recovery support, our trained caregivers provide compassionate, professional care tailored to each individual's specific condition and requirements.
                </p>

                {/* Key Features */}
                <div className="grid md:grid-cols-3 gap-6 mt-8">
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-full flex items-center justify-center mb-3">
                      <Brain className="h-6 w-6 text-secondary dark:text-secondary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Dementia Expertise</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Specialized training in dementia and Alzheimer's care with compassionate, patient-centered approaches</p>
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-full flex items-center justify-center mb-3">
                      <Activity className="h-6 w-6 text-secondary dark:text-secondary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Recovery Support</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Expert post-surgery and rehabilitation care to ensure safe, comfortable recovery at home</p>
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-full flex items-center justify-center mb-3">
                      <Heart className="h-6 w-6 text-secondary dark:text-secondary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Personalized Care</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Customized care plans designed to meet individual needs and specific medical conditions</p>
                  </div>
                </div>

                <div className="mt-8">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                      <span>Learn About Expert Specialized Care Services</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Services Section */}
          <section
            ref={servicesRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="services"
          >
            <motion.div
              initial="hidden"
              animate={servicesInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    What We Offer
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Comprehensive Home Care Services
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Each service is delivered with care, professionalism, and a commitment to improving quality of life.
                </p>
              </div>

              {/* Service cards - Updated to 3 columns for 6 services */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                {services.map((service, index) => (
                  <div key={index} className="group">
                    <Card className="h-full overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                      <div className="relative h-[250px] overflow-hidden">
                        <Image
                          src={service.image || "/placeholder.svg?height=250&width=400"}
                          alt={`${service.title} - Professional home care services`}
                          width={400}
                          height={250}
                          className="object-cover object-top w-full h-full transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-70"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <service.icon className="h-5 w-5 text-white" />
                            <h3 className="text-lg font-medium text-white group-hover:text-white transition-colors duration-300">
                              {service.title}
                            </h3>
                          </div>
                        </div>
                      </div>

                      <CardContent className="p-5 flex flex-col">
                        <p className="text-gray-600 dark:text-gray-300 font-light mb-4 flex-grow text-sm">
                          {service.description}
                        </p>

                        <div className="mt-auto space-y-3">
                          <Link
                            href={`/services?service=${service.id}`}
                            className="inline-flex items-center text-sm font-medium text-primary dark:text-primary-light hover:text-primary-dark dark:hover:text-primary-light transition-colors duration-300"
                          >
                            Learn more about {service.title.toLowerCase()}
                            <ChevronRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                          </Link>

                          {/* Service-specific CTA buttons */}
                          <div className="flex gap-2">
                            <Link href="/consultation" className="flex-1">
                              <Button
                                size="sm"
                                className="w-full text-xs bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white transition-all duration-300"
                              >
                                {service.id === '24-hour-care' ? 'Get 24/7 Care' :
                                 service.id === 'dementia-care' ? 'Memory Care Help' :
                                 service.id === 'post-surgery-care' ? 'Recovery Support' :
                                 service.id === 'respite-care' ? 'Get Relief Now' :
                                 service.id === 'personal-care' ? 'Personal Care' :
                                 service.id === 'companion-care' ? 'Find Companion' :
                                 'Get Care Info'}
                              </Button>
                            </Link>
                            <a href="tel:**********" className="flex-shrink-0">
                              <Button
                                size="sm"
                                variant="outline"
                                className="border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300"
                              >
                                <Phone className="h-3 w-3" />
                              </Button>
                            </a>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>

              <div className="mt-16 text-center">
                <h3 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Ready to Get Started with Aneeko Rapha Healthcare Services?
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                  Contact us today to discuss your specific care needs and schedule a free consultation in Tarrant County, TX.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                      <span>Schedule Free Consultation</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                  <a href="tel:**********">
                    <Button variant="outline" className="rounded-full border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 h-auto transition-all duration-300 flex items-center gap-2 group">
                      <Phone className="h-4 w-4" />
                      <span>Call (*************</span>
                    </Button>
                  </a>
                </div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 right-0 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 left-0 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* Trust Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Why Families Trust Us
                </h2>

                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
                  {trustBenefits.map((benefit, index) => (
                    <div
                      key={index}
                      className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md"
                    >
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                        <benefit.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-medium mb-2">{benefit.title}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{benefit.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Refer Us Section */}
          <section
            ref={referRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
            id="refer"
          >
            <motion.div
              initial="hidden"
              animate={referInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Refer With Confidence
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Share the Gift of Compassionate Care
                </h2>
                <div className="w-20 h-1 bg-primary dark:bg-primary-light mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Know someone who could benefit from our services? Your referral helps us extend our compassionate care
                  to more families in need.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div variants={fadeInLeft} className="relative w-full">
                  <div className="w-full h-0 pb-[75%] relative rounded-2xl overflow-hidden shadow-xl">
                    <Image
                      src="/in11.jpg"
                      alt="Caregiver helping senior"
                      fill
                      className="object-cover object-top rounded-2xl"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <p className="text-white text-lg font-medium">Making a difference in someone's life</p>
                    </div>
                  </div>

                  <div className="absolute -bottom-10 -right-10 md:bottom-auto md:top-1/2 md:-translate-y-1/2 md:-right-16 w-32 h-32 rounded-full border-8 border-white dark:border-gray-800 shadow-xl overflow-hidden">
                    <div className="w-full h-full bg-secondary-light dark:bg-secondary/20 flex items-center justify-center">
                      <Share2 className="h-12 w-12 text-primary" />
                    </div>
                  </div>
                </motion.div>

                <motion.div variants={fadeInRight} className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">
                    Why Refer to Aneeko Rapha Healthcare Services?
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                        <ThumbsUp className="h-6 w-6 text-primary dark:text-primary-light" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Trusted Quality Care</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          Our caregivers are thoroughly vetted, certified, and supervised to provide exceptional care.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-secondary-light dark:bg-secondary/20 flex items-center justify-center flex-shrink-0">
                        <Heart className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Compassionate Approach</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          We treat each client with dignity, respect, and genuine care, focusing on their unique needs.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-neutral dark:bg-gray-700 flex items-center justify-center flex-shrink-0">
                        <Gift className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Referral Appreciation</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          We value your trust and confidence in our services. Ask about our referral appreciation
                          program.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-6">
                    <Link href="/refer">
                      <Button className="rounded-full bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                        <span>Refer Someone Today</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 right-20 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 left-20 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* About Us Section */}
          <section
            ref={aboutRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
            id="about"
          >
            <motion.div
              initial="hidden"
              animate={aboutInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    About Aneeko Rapha
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Your Trusted Home Care Agency
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Aneeko Rapha Healthcare Services provides exceptional, personalized home care for seniors and adults with disabilities in Tarrant County, TX, built on integrity, empathy, and excellence.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div variants={fadeInLeft} className="w-full">
                  <div className="w-full h-0 pb-[75%] relative rounded-2xl overflow-hidden shadow-xl">
                    <Image
                      src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/4e8a4-our-commitment-to-quality.jpeg"
                      alt="Aneeko Rapha Healthcare Services - Professional caregivers"
                      fill
                      className="object-cover rounded-2xl"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                      <p className="text-white text-lg font-medium">Our commitment to quality care</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div variants={fadeInRight} className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">Commitment to Quality & Excellence</h3>

                  <p className="text-gray-600 dark:text-gray-300">
                    At Aneeko Rapha Healthcare Services, we understand the importance of independence and comfort for seniors and adults with disabilities. Our dedicated caregivers provide personalized care and assistance, allowing your loved ones to thrive in the familiar surroundings of their own home. We are committed to maintaining the highest standards of care and continuously seeking ways to improve and enhance our services in Tarrant County, TX and surrounding areas.
                  </p>

                  <div className="space-y-4 pt-4">
                    {coreValues.map((value, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                          <value.icon className="h-5 w-5 text-primary dark:text-primary-light" />
                        </div>
                        <div>
                          <h4 className="text-base font-medium text-gray-800 dark:text-gray-100">{value.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300">{value.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="pt-6">
                    <Link href="/consultation">
                      <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                        <span>Schedule Free Consultation</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 left-20 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 right-20 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* Care Approach Section */}
          <section className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950">
            <div className={cn(containerClass)}>
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                    Our Approach
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Our Personalized Care Process
                </h2>
                <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  We follow a comprehensive 6-step approach to ensure the highest quality of personalized care for every client.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    step: "01",
                    title: "Initial Assessment",
                    description: "We meet with you and your loved ones to assess their care needs and understand their preferences.",
                    icon: ClipboardList,
                  },
                  {
                    step: "02",
                    title: "Safety Evaluation",
                    description: "We conduct a thorough safety evaluation of the home environment to ensure optimal care conditions.",
                    icon: Shield,
                  },
                  {
                    step: "03",
                    title: "Care Plan Creation",
                    description: "We create a personalized care plan and schedule that accommodates specific needs and preferences.",
                    icon: Heart,
                  },
                  {
                    step: "04",
                    title: "Caregiver Assignment",
                    description: "We carefully select and assign a caregiver who matches your loved one's personality and care requirements.",
                    icon: Users,
                  },
                  {
                    step: "05",
                    title: "Ongoing Monitoring",
                    description: "We continuously monitor care quality and adjust plans as needs change over time.",
                    icon: Award,
                  },
                  {
                    step: "06",
                    title: "Feedback Integration",
                    description: "We actively seek feedback from clients and families to continuously improve our services.",
                    icon: HeartHandshake,
                  },
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="relative"
                  >
                    <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl overflow-hidden group">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-secondary to-secondary-light flex items-center justify-center">
                              <item.icon className="h-6 w-6 text-white" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <span className="text-2xl font-bold text-secondary">{item.step}</span>
                              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">{item.title}</h3>
                            </div>
                            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{item.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              <div className="mt-16 text-center">
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                  Our flexible care plans can be adjusted when the needs of your loved ones change, ensuring continuous, appropriate care.
                </p>
                <Link href="/consultation">
                  <Button className="rounded-full bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                    <span>Start Your Care Assessment</span>
                    <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </div>
          </section>

          {/* Testimonials Section */}
          <section
            ref={testimonialsRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="testimonials"
          >
            <motion.div
              initial="hidden"
              animate={testimonialsInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary-light dark:bg-secondary/20 text-primary rounded-full text-sm font-medium mb-4">
                    Testimonials
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  What Our Clients Say
                </h2>
                <div className="w-20 h-1 bg-primary dark:bg-primary-light mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Read stories from families who have experienced the Aneeko Rapha Healthcare Services difference.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {testimonials.map((testimonial, index) => (
                  <Card
                    key={index}
                    className="border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center mb-4">
                        <Quote className="h-5 w-5 text-primary mr-2" />
                        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">Client Feedback</h3>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 font-light mb-4">{testimonial.quote}</p>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-primary dark:text-primary-light">
                            {testimonial.author}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{testimonial.location}</p>
                        </div>
                        <Star className="h-5 w-5 text-yellow-500" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>
          </section>

          {/* Vision & Mission Section */}
          <section className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
            <div className={cn(containerClass)}>
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Our Purpose
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Vision & Mission
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Our commitment to excellence drives everything we do at Aneeko Rapha Healthcare Services.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
                {/* Vision */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="relative"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl overflow-hidden">
                    <CardContent className="p-8">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark flex items-center justify-center">
                          <Award className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Our Vision</h3>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                        To become the leading provider of accessible, innovative, and compassionate healthcare solutions, enhancing the well-being of individuals and communities.
                      </p>
                      <div className="mt-6 p-4 bg-primary/5 dark:bg-primary/10 rounded-lg border-l-4 border-primary">
                        <p className="text-sm text-gray-600 dark:text-gray-300 italic">
                          We envision a future where quality healthcare is accessible to all, delivered with innovation and compassion.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Mission */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl overflow-hidden">
                    <CardContent className="p-8">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark flex items-center justify-center">
                          <Heart className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Our Mission</h3>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-4">
                        At Aneeko Rapha Health Care Services, our mission is to provide exceptional healthcare services with integrity, empathy, and excellence. We strive to deliver comprehensive and personalized care that meets the diverse needs of our patients.
                      </p>
                      <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                        Through continuous innovation, collaboration, and a commitment to excellence, we aim to improve health outcomes and empower individuals to live healthier, happier lives.
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              <div className="mt-16 text-center">
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-3xl mx-auto text-lg">
                  Our vision and mission guide every decision we make, ensuring that we consistently deliver the highest quality of care to our clients in Tarrant County, TX and surrounding areas.
                </p>
                <Link href="/consultation">
                  <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                    <span>Experience Our Care</span>
                    <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-20 left-20 w-64 h-64 bg-primary/10 dark:bg-primary/5 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-20 right-20 w-80 h-80 bg-secondary/10 dark:bg-secondary/5 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* Trust Indicators Section */}
          <section className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950">
            <div className={cn(containerClass)}>
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                    Trust & Quality
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Why Families Trust Aneeko Rapha
                </h2>
                <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Our commitment to excellence is backed by rigorous standards, comprehensive training, and proven results.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[
                  {
                    icon: Shield,
                    title: "Licensed & Insured",
                    description: "Fully licensed home care agency with comprehensive insurance coverage for your peace of mind.",
                    stat: "100%",
                    statLabel: "Compliance"
                  },
                  {
                    icon: Award,
                    title: "Certified Caregivers",
                    description: "All caregivers undergo thorough background checks, certification, and ongoing professional development.",
                    stat: "24/7",
                    statLabel: "Training Support"
                  },
                  {
                    icon: HeartHandshake,
                    title: "Quality Assurance",
                    description: "Regular quality assessments and client feedback ensure consistently excellent care delivery.",
                    stat: "5-Star",
                    statLabel: "Client Reviews"
                  },
                  {
                    icon: Users,
                    title: "Safety Protocols",
                    description: "Comprehensive safety training and emergency procedures to ensure the highest level of protection.",
                    stat: "Tarrant County",
                    statLabel: "Local Experts"
                  },
                ].map((indicator, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="relative"
                  >
                    <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl overflow-hidden group text-center">
                      <CardContent className="p-6">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-4 flex items-center justify-center">
                          <indicator.icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3">{indicator.title}</h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4">{indicator.description}</p>
                        <div className="mt-4 p-3 bg-secondary/5 dark:bg-secondary/10 rounded-lg">
                          <div className="text-2xl font-bold text-secondary dark:text-secondary-light">{indicator.stat}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide">{indicator.statLabel}</div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              <div className="mt-16 text-center">
                <div className="bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-2xl p-8 max-w-4xl mx-auto">
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
                    Our Commitment to Excellence
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                    At Aneeko Rapha Healthcare Services, we maintain the highest standards through continuous training,
                    quality monitoring, and a genuine commitment to improving the lives of our clients. Our caregivers
                    are selected not only for their professional qualifications but also for their warmth, compassion,
                    and dedication to those in their care.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link href="/consultation">
                      <Button className="bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2 group">
                        <span>Learn About Our Standards</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                    <a href="tel:**********">
                      <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        <span>Speak with Our Team</span>
                      </Button>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-40 left-10 w-32 h-32 bg-secondary/10 dark:bg-secondary/5 rounded-full blur-2xl -z-10"></div>
            <div className="absolute bottom-40 right-10 w-40 h-40 bg-primary/10 dark:bg-primary/5 rounded-full blur-2xl -z-10"></div>
          </section>

          {/* Why Choose Us Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Why Choose Aneeko Rapha Healthcare Services.
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Your Trusted Partner in Home Care
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>

                <div className="grid md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">Licensed & Insured</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      All our caregivers are certified, licensed, and fully insured for your peace of mind.
                    </p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-secondary to-secondary-dark mx-auto mb-4 flex items-center justify-center">
                      <Heart className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">Compassionate Care</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      We provide heartfelt, personalized care that treats each client with dignity and respect.
                    </p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                      <Clock className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">24/7 Availability</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Round-the-clock support and emergency availability when you need us most.
                    </p>
                  </div>
                </div>

                <div className="mt-12">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                      <span>Start Your Care Journey Today</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Payment Options Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                    Payment Options
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Flexible Payment Solutions
                </h2>
                <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-8"></div>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  Aneeko Rapha Healthcare Services accepts multiple payment options to make quality care accessible.
                  We work with families to explore all available options and ensure you receive the care you need.
                </p>

                {/* Payment Methods Grid */}
                <div className="grid md:grid-cols-3 gap-6 mt-8">
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Shield className="w-6 h-6 text-secondary" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Long-Term Care Insurance</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">We accept most long-term care insurance policies to help cover your care costs.</p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2 4h20v16H2V4zm2 2v12h16V6H4zm2 2h12v2H6V8zm0 4h8v2H6v-2z"/>
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Private Pay</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Credit cards, debit cards, and other private payment methods accepted.</p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <HeartHandshake className="w-6 h-6 text-secondary" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Payment Plans</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Flexible payment arrangements available to fit your family's budget.</p>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-xl">
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    <strong>Need help with payment options?</strong> Our team will work with you to explore all available
                    payment solutions and insurance benefits to make quality care affordable for your family.
                  </p>
                  <Link href="/consultation">
                    <Button className="bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2 group mx-auto">
                      <span>Discuss Payment Options</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Latest Blog Posts Section */}
          <LatestBlogPosts />



          {/* Contact Section */}
          <section
            ref={contactRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="contact"
          >
            <motion.div
              initial="hidden"
              animate={contactInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Contact Us
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Contact Aneeko Rapha Healthcare Services
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  We're here to answer your questions and help you find the right home care solution for your loved ones in Tarrant County, TX.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <div className="w-full h-0 pb-[75%] relative">
                    <Image
                      src="/1n13.jpg"
                      alt="Journey of Care team"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-lg font-medium">Professional home care services</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">
                    Let's Discuss Your Home Care Needs
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300">
                    Contact us today to schedule a free consultation and learn how Aneeko Rapha Healthcare Services can
                    provide exceptional, personalized care for your loved ones in Tarrant County, TX and surrounding areas.
                  </p>

                  <div className="space-y-4">
                    {contactInfo.map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <item.icon className="h-5 w-5 text-primary dark:text-primary-light" />
                        <a
                          href={item.label === "Phone" ? `tel:${item.value}` : `mailto:${item.value}`}
                          className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-300"
                        >
                          {item.value}
                        </a>
                      </div>
                    ))}
                  </div>

                  <div className="pt-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Link href="/consultation">
                        <Button className="rounded-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                          <span>Schedule Free Consultation</span>
                          <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                      </Link>
                      <a href="tel:**********">
                        <Button variant="outline" className="rounded-full border-primary text-primary hover:bg-primary hover:text-white px-6 py-2 h-auto transition-all duration-300 flex items-center gap-2 group">
                          <Phone className="h-4 w-4" />
                          <span>Call Now</span>
                        </Button>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </section>

          {/* FAQ Section */}
          <section
            ref={faqRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="faq"
          >
            <motion.div
              initial="hidden"
              animate={faqInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    FAQ
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Frequently Asked Questions About Our Home Care Services
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Find answers to common questions about our home care services and care approach.
                </p>
              </div>

              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <Card
                    key={index}
                    className="border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                  >
                    <CardContent className="p-6">
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">{faq.question}</h3>
                      <p className="text-gray-600 dark:text-gray-300 font-light">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>
          </section>

          {/* Local SEO Section */}
          <section className="py-16 bg-gradient-to-b from-white to-gray-50 dark:from-gray-950 dark:to-gray-900 border-t border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-2xl md:text-3xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Serving Tarrant County, Texas with Excellence
                </h2>
                <div className="text-gray-600 dark:text-gray-300 space-y-4 text-sm leading-relaxed">
                  <p>
                    <strong>Aneeko Rapha Healthcare Services</strong> is proud to be Tarrant County's trusted home care agency,
                    providing exceptional in-home care services to seniors and adults with disabilities throughout
                    Tarrant County, Texas and surrounding communities. Our comprehensive home care services include
                    personal care, dementia care, companion care, homemaking services, respite care, post-surgery care,
                    and 24-hour care.
                  </p>
                  <p>
                    As a locally-focused home care provider in Tarrant County, TX, we understand the unique needs of our
                    community. Our certified caregivers are specially trained to provide compassionate, professional
                    care that allows your loved ones to age in place safely and comfortably. Whether you need short-term
                    post-surgery support or long-term 24-hour care in Tarrant County, we're here to help.
                  </p>
                  <p>
                    Contact Aneeko Rapha Healthcare Services today at <strong>(*************</strong> to learn more about
                    our home care services in Tarrant County, Texas. We offer free consultations to discuss your specific
                    care needs and create a personalized care plan that's right for your family.
                  </p>
                </div>
                <div className="mt-8 flex flex-wrap justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                  <span className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">Tarrant County Home Care</span>
                  <span className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">Texas Senior Care</span>
                  <span className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">Tarrant County Caregivers</span>
                  <span className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">24-Hour Care Tarrant County</span>
                  <span className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">Dementia Care Texas</span>
                </div>
              </div>
            </div>
          </section>
        </main>

        <Footer />
      </div>
    </>
  )
}
