import type { Metadata } from "next"
import ConsultationClient from "./consultation-client"

export const metadata: Metadata = {
  title: "Schedule Free Consultation | Aneeko Rapha Healthcare Services - Tarrant County, TX",
  description:
    "Schedule your free home care consultation with Aneeko Rapha Healthcare Services in Tarrant County, TX. Get personalized care plans for seniors and adults with disabilities. No obligation assessment.",
  keywords: [
    "free consultation Tarrant County TX",
    "home care assessment Tarrant County",
    "senior care consultation",
    "Aneeko Rapha consultation",
    "home health evaluation Tarrant County",
    "care plan assessment",
    "free home care evaluation",
  ],
  alternates: {
    canonical: "/consultation",
  },
  openGraph: {
    title: "Schedule Free Consultation | Aneeko Rapha Healthcare Services - Tarrant County, TX",
    description:
      "Schedule your free home care consultation with Aneeko Rapha Healthcare Services in Tarrant County, TX. Get personalized care plans for seniors and adults with disabilities. No obligation assessment.",
    url: "https://araphahealthcare.com/consultation",
  },
}

export default function ConsultationPage() {
  return <ConsultationClient />
}

