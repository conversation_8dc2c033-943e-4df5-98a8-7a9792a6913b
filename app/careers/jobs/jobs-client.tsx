"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { ArrowLeft, Briefcase, MapPin, Clock, DollarSign, Users, Heart, Mail, Phone } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function JobsClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Job postings data for Aneeko Rapha Healthcare Services
  const jobPostings = [
    {
      id: "hha-001",
      title: "Certified Home Health Aide (HHA)",
      location: "Tarrant County, TX",
      type: "Full-Time / Part-Time",
      salary: "$16-20/hour",
      posted: "2 days ago",
      description: "We are seeking compassionate and skilled Certified Home Health Aides to join our team at Aneeko Rapha Healthcare Services. As an HHA, you will provide quality personal care to clients in their homes throughout Tarrant County, TX, helping them maintain their health and independence.",
      responsibilities: [
        "Assist clients with activities of daily living (bathing, dressing, grooming)",
        "Take and record vital signs accurately",
        "Assist with mobility, transfers, and positioning",
        "Provide companionship and emotional support",
        "Help with medication reminders (no administration)",
        "Maintain client hygiene and comfort",
        "Document care provided and report changes in condition",
        "Communicate effectively with nursing staff and families",
      ],
      qualifications: [
        "Current HHA certification in Texas",
        "High school diploma or equivalent",
        "Prior experience as an HHA (1+ year preferred)",
        "Valid driver's license and reliable transportation",
        "Compassionate and patient demeanor",
        "Ability to pass a background check and drug screen",
        "Physical ability to assist clients with mobility",
        "CPR certification preferred",
      ],
      benefits: [
        "Competitive hourly wages ($16-20/hour)",
        "Flexible scheduling options",
        "Health insurance options",
        "Paid time off",
        "Professional development opportunities",
        "Supportive team environment",
      ],
    },
    {
      id: "pca-001",
      title: "Personal Care Assistant",
      location: "Tarrant County, TX",
      type: "Full-Time / Part-Time",
      salary: "$14-18/hour",
      posted: "1 week ago",
      description: "We are seeking compassionate and reliable Personal Care Assistants to join our team at Aneeko Rapha Healthcare Services. As a Personal Care Assistant, you will provide personal care and assistance to clients in their homes throughout Tarrant County, TX, helping them maintain their independence and quality of life.",
      responsibilities: [
        "Assist clients with personal care (bathing, grooming, dressing)",
        "Provide companionship and emotional support",
        "Help with light housekeeping and meal preparation",
        "Assist with mobility and transfers",
        "Provide medication reminders",
        "Accompany clients to appointments when needed",
        "Monitor client safety and report concerns",
        "Maintain accurate documentation of care provided",
      ],
      qualifications: [
        "High school diploma or equivalent",
        "Previous experience in caregiving preferred",
        "Valid driver's license and reliable transportation",
        "Compassionate and patient personality",
        "Ability to pass background check and drug screen",
        "Physical ability to assist with client care",
        "Good communication skills",
        "Flexibility with scheduling",
      ],
      benefits: [
        "Competitive hourly wages ($14-18/hour)",
        "Flexible scheduling",
        "Training provided",
        "Opportunity for advancement",
        "Supportive work environment",
        "Recognition programs",
      ],
    },
    {
      id: "cc-001",
      title: "Care Coordinator",
      location: "Tarrant County, TX",
      type: "Full-Time",
      salary: "$45,000-55,000/year",
      posted: "3 days ago",
      description: "We are seeking an experienced Care Coordinator to join our team at Aneeko Rapha Healthcare Services. The Care Coordinator will be responsible for coordinating care plans, managing client relationships, and supporting our care team to ensure the highest quality of service delivery.",
      responsibilities: [
        "Develop and manage individualized care plans",
        "Coordinate services between clients, families, and care team",
        "Conduct initial assessments and ongoing evaluations",
        "Manage caregiver schedules and assignments",
        "Ensure compliance with regulations and standards",
        "Provide support and guidance to care team",
        "Handle client and family communications",
        "Maintain accurate documentation and records",
      ],
      qualifications: [
        "Bachelor's degree in healthcare, social work, or related field",
        "2+ years experience in healthcare coordination",
        "Knowledge of home care regulations",
        "Strong organizational and communication skills",
        "Proficiency in healthcare software systems",
        "Valid driver's license",
        "Ability to work independently",
        "Bilingual (English/Spanish) preferred",
      ],
      benefits: [
        "Competitive annual salary ($45,000-55,000)",
        "Comprehensive health insurance",
        "Retirement plan with company match",
        "Paid vacation and sick leave",
        "Professional development opportunities",
        "Company vehicle or mileage reimbursement",
      ],
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Current Job Openings"
        subtitle="Join our team at Aneeko Rapha Healthcare Services and make a difference in Tarrant County, TX"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg"
        imageAlt="Healthcare team working together"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          <div className="mb-8">
            <Link
              href="/careers"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Careers
            </Link>
          </div>

          {/* Job Listings */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn}>
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Current Openings
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Available Positions
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Explore our current job openings and find the perfect opportunity to join our compassionate team of healthcare professionals.
              </p>
            </div>

            <div className="space-y-8">
              {jobPostings.map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                    <CardContent className="p-8">
                      {/* Job Header */}
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                        <div className="mb-4 lg:mb-0">
                          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
                            {job.title}
                          </h2>
                          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                            <div className="flex items-center gap-1">
                              <MapPin className="h-4 w-4" />
                              <span>{job.location}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Briefcase className="h-4 w-4" />
                              <span>{job.type}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              <span>{job.salary}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>Posted {job.posted}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white" asChild>
                            <a href={`mailto:<EMAIL>?subject=Application for ${job.title}`}>
                              Apply Now
                            </a>
                          </Button>
                        </div>
                      </div>

                      {/* Job Description */}
                      <div className="mb-6">
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {job.description}
                        </p>
                      </div>

                      {/* Job Details Accordion */}
                      <Accordion type="single" collapsible className="w-full">
                        <AccordionItem value="responsibilities">
                          <AccordionTrigger className="text-left">
                            <span className="font-semibold">Responsibilities</span>
                          </AccordionTrigger>
                          <AccordionContent>
                            <ul className="space-y-2">
                              {job.responsibilities.map((responsibility, idx) => (
                                <li key={idx} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                                  <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                                  <span>{responsibility}</span>
                                </li>
                              ))}
                            </ul>
                          </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="qualifications">
                          <AccordionTrigger className="text-left">
                            <span className="font-semibold">Qualifications</span>
                          </AccordionTrigger>
                          <AccordionContent>
                            <ul className="space-y-2">
                              {job.qualifications.map((qualification, idx) => (
                                <li key={idx} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                                  <span className="w-2 h-2 bg-secondary rounded-full mt-2 flex-shrink-0"></span>
                                  <span>{qualification}</span>
                                </li>
                              ))}
                            </ul>
                          </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="benefits">
                          <AccordionTrigger className="text-left">
                            <span className="font-semibold">Benefits</span>
                          </AccordionTrigger>
                          <AccordionContent>
                            <ul className="space-y-2">
                              {job.benefits.map((benefit, idx) => (
                                <li key={idx} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                                  <span>{benefit}</span>
                                </li>
                              ))}
                            </ul>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mt-20">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Ready to Apply?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                Join our team and make a meaningful difference in the lives of seniors and adults with disabilities in Tarrant County, TX.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2 group" asChild>
                  <a href="mailto:<EMAIL>">
                    <Mail className="h-5 w-5" />
                    <span>Email Your Resume</span>
                  </a>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2" asChild>
                  <a href="tel:6822289234">
                    <Phone className="h-5 w-5" />
                    <span>Call (*************</span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
