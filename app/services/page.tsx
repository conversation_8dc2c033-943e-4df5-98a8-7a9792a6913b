import type { Metadata } from "next"
import ServicesPageClient from "./services-client"

export const metadata: Metadata = {
  title: "Home Care Services in Tarrant County, TX | Aneeko Rapha Healthcare Services",
  description:
    "Comprehensive home care services by Aneeko Rapha Healthcare Services including personal care, dementia care, companion care, homemaking, respite care, post-surgery care, and 24-hour care. Serving Tarrant County, TX.",
  keywords: [
    "home care services Tarrant County TX",
    "personal care services Tarrant County",
    "dementia care Tarrant County",
    "companion care Tarrant County",
    "homemaking services Tarrant County",
    "respite care Tarrant County",
    "post-surgery care Tarrant County",
    "24-hour care Tarrant County",
    "Aneeko Rapha Healthcare Services",
    "home health care Tarrant County",
    "senior care Tarrant County",
  ],
  alternates: {
    canonical: "/services",
  },
  openGraph: {
    title: "Home Care Services in Tarrant County, TX | Aneeko Rapha Healthcare Services",
    description:
      "Comprehensive home care services by Aneeko Rapha Healthcare Services including personal care, dementia care, companion care, homemaking, respite care, post-surgery care, and 24-hour care. Serving Tarrant County, TX.",
    url: "https://araphahealthcare.com/services",
  },
}

export default function ServicesPage() {
  return <ServicesPageClient />
}
