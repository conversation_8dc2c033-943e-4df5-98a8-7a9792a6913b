import { type NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"
import { EmailTemplate, AdminEmailTemplate } from "@/components/email-template"

export async function POST(request: NextRequest) {
  // Lazily create the Resend client so build-time doesn’t require the env key.
  const resend = new Resend(process.env.RESEND_API_KEY ?? "")
  try {
    const body = await request.json()
    const { name, email, message, formType = "contact", ...formData } = body

    if (!name || !email || !message) {
      return NextResponse.json({ error: "Name, email, and message are required" }, { status: 400 })
    }

    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>"
    const fromEmail = process.env.DEFAULT_FROM_EMAIL || "Journey of Care <<EMAIL>>"

    // Determine subject line based on form type
    let subjectPrefix = "Journey of Care - "
    switch (formType) {
      case "consultation":
        subjectPrefix += "New Consultation Request"
        break
      case "referral":
        subjectPrefix += "New Referral Submission"
        break
      default:
        subjectPrefix += "New Contact Form Submission"
    }

    // Send confirmation email to user
    const userEmailData = await resend.emails.send({
      from: fromEmail,
      to: [email],
      subject: `Thank you for contacting Journey of Care`,
      html: EmailTemplate({
        name,
        message:
          "Thank you for reaching out to Journey of Care. We've received your message and will get back to you soon.",
        formType,
        formData: { name, email, ...formData },
      }),
    })

    // Send notification email to admin
    const adminEmailData = await resend.emails.send({
      from: fromEmail,
      to: [adminEmail],
      subject: `${subjectPrefix} from ${name}`,
      html: AdminEmailTemplate({
        name,
        message,
        formType,
        formData: { name, email, message, ...formData },
      }),
    })

    console.log("Journey of Care - Emails sent successfully:", { userEmailData, adminEmailData })

    return NextResponse.json(
      {
        message:
          "Thank you for contacting Journey of Care! We've received your message and will respond within 24-48 hours.",
        success: true,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("Journey of Care - Error sending email:", error)
    return NextResponse.json(
      {
        error: "Failed to send message. Please try again or call us directly at (832) 446-0705.",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
