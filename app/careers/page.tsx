import type { Metadata } from "next"
import CareersClient from "./careers-client"

export const metadata: Metadata = {
  title: "Careers | Aneeko Rapha Healthcare Services - Tarrant County, TX",
  description:
    "Join our team at Aneeko Rapha Healthcare Services in Tarrant County, TX. We offer rewarding career opportunities in home healthcare with competitive benefits and professional growth.",
  keywords: [
    "careers Tarrant County TX",
    "home care jobs Tarrant County",
    "healthcare jobs Tarrant County",
    "Aneeko Rapha careers",
    "CNA jobs Tarrant County",
    "HHA jobs Tarrant County",
    "caregiver jobs Tarrant County",
  ],
  alternates: {
    canonical: "/careers",
  },
  openGraph: {
    title: "Careers | Aneeko Rapha Healthcare Services - Tarrant County, TX",
    description:
      "Join our team at Aneeko Rapha Healthcare Services in Tarrant County, TX. We offer rewarding career opportunities in home healthcare with competitive benefits and professional growth.",
    url: "https://araphahealthcare.com/careers",
  },
}

export default function CareersPage() {
  return <CareersClient />
}
