import type React from "react"

interface JobApplicationEmailTemplateProps {
  name: string
  position: string
  applicationData?: Record<string, any>
}

export const JobApplicationEmailTemplate: React.FC<JobApplicationEmailTemplateProps> = ({
  name,
  position,
  applicationData = {},
}) => {
  const logoUrl = "https://journey-of-care.com/images/journey-of-care-logo.png"

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Application Received - Journey of Care</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f8fdf9;">
      <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9;">
        <tr>
          <td align="center" style="padding: 20px;">
            <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 600px;">
              
              <!-- Header -->
              <tr>
                <td align="center" style="background-color: #E6E6FA; padding: 30px 20px; border-radius: 12px 12px 0 0;">
                  <img src="${logoUrl}" alt="Journey of Care" style="max-width: 200px; height: auto; display: block;" />
                </td>
              </tr>
              
              <!-- Content -->
              <tr>
                <td style="padding: 40px 30px;">
                  <!-- Success Badge -->
                  <table cellpadding="0" cellspacing="0" style="margin-bottom: 20px;">
                    <tr>
                      <td style="background-color: #E6E6FA; color: #4A4A4A; padding: 8px 20px; border-radius: 25px; font-size: 14px; font-weight: 600; font-family: Arial, sans-serif;">
                        ✓ Application Received
                      </td>
                    </tr>
                  </table>
                  
                  <h2 style="color: #333; font-family: Arial, sans-serif; margin: 0 0 10px 0;">Thank you, ${name}!</h2>
                  <p style="color: #8B5A8C; font-size: 20px; font-weight: 600; margin: 10px 0; font-family: Arial, sans-serif;">Position Applied For: ${position}</p>
                  
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">We've successfully received your job application for the <strong>${position}</strong> position at Journey of Care. Thank you for your interest in joining our compassionate team!</p>
                  
                  <!-- Next Steps Box -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8f4ff; border-left: 4px solid #E6E6FA; border-radius: 8px; margin: 25px 0;">
                    <tr>
                      <td style="padding: 20px;">
                        <h3 style="margin: 0 0 15px 0; color: #8B5A8C; font-family: Arial, sans-serif;">What happens next?</h3>
                        <ul style="margin: 0; padding-left: 20px; color: #333; font-family: Arial, sans-serif;">
                          <li>Our HR team will review your application and resume</li>
                          <li>If your qualifications match our requirements, we'll contact you within <span style="color: #8B5A8C; font-weight: 600;">3-5 business days</span></li>
                          <li>Qualified candidates will be invited for an initial phone screening</li>
                          <li>Successful candidates will be scheduled for an in-person interview</li>
                        </ul>
                      </td>
                    </tr>
                  </table>
                  
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">We appreciate the time you took to apply and look forward to potentially welcoming you to our team of dedicated caregivers who make a difference in people's lives every day.</p>
                  
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">If you have any questions about your application or our hiring process, please don't hesitate to reach out to us.</p>
                  
                  <!-- Contact Info Box -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9; border: 1px solid #E6E6FA; border-radius: 12px; margin-top: 30px;">
                    <tr>
                      <td style="padding: 25px;">
                        <h3 style="color: #8B5A8C; margin: 0 0 15px 0; font-size: 18px; font-family: Arial, sans-serif;">Contact Information</h3>
                        <p style="margin: 8px 0; color: #4a5568; font-family: Arial, sans-serif;">📞 <strong>Phone:</strong> (*************</p>
                        <p style="margin: 8px 0; color: #4a5568; font-family: Arial, sans-serif;">✉️ <strong>Email:</strong> <EMAIL></p>
                        <p style="margin: 8px 0; color: #4a5568; font-family: Arial, sans-serif;">📍 <strong>Service Area:</strong> Conroe, TX & Surrounding Communities</p>
                        <p style="margin: 15px 0 5px 0; color: #8B5A8C; font-weight: 600; font-family: Arial, sans-serif;">Your Peace of Mind Is Our Promise of Care</p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              
              <!-- Footer -->
              <tr>
                <td style="background-color: #f8f4ff; padding: 25px; text-align: center; border-radius: 0 0 12px 12px; border-top: 1px solid #E6E6FA;">
                  <p style="margin: 0 0 10px 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">&copy; ${new Date().getFullYear()} Journey of Care. All rights reserved.</p>
                  <p style="margin: 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">This is an automated message confirming receipt of your job application.</p>
                </td>
              </tr>
              
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `
}

export const AdminJobApplicationEmailTemplate: React.FC<JobApplicationEmailTemplateProps> = ({
  name,
  position,
  applicationData = {},
}) => {
  const logoUrl = "https://journey-of-care.com/images/journey-of-care-logo.png"

  // Format application data for admin email
  const applicationDataHtml = Object.entries(applicationData)
    .filter(([key, value]) => value && value !== "")
    .map(([key, value]) => {
      const formattedKey = key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase())
        .replace(/([a-z])([A-Z])/g, "$1 $2")

      return `<tr>
        <td style="padding: 12px 15px; border-bottom: 1px solid #E6E6FA; font-weight: 600; color: #8B5A8C; background-color: #f8fdf9; font-family: Arial, sans-serif;">${formattedKey}</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #E6E6FA; color: #4a5568; font-family: Arial, sans-serif;">${value}</td>
      </tr>`
    })
    .join("")

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Job Application - ${position} - Journey of Care</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f8fdf9;">
      <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9;">
        <tr>
          <td align="center" style="padding: 20px;">
            <table width="700" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 700px;">
              
              <!-- Header -->
              <tr>
                <td align="center" style="background-color: #E6E6FA; padding: 30px 20px; border-radius: 12px 12px 0 0;">
                  <img src="${logoUrl}" alt="Journey of Care" style="max-width: 200px; height: auto; display: block;" />
                </td>
              </tr>
              
              <!-- Content -->
              <tr>
                <td style="padding: 40px 30px;">
                  <!-- Alert Badge -->
                  <table cellpadding="0" cellspacing="0" style="margin-bottom: 20px;">
                    <tr>
                      <td style="background-color: #FFC0CB; color: #4A4A4A; padding: 8px 20px; border-radius: 25px; font-size: 14px; font-weight: 600; font-family: Arial, sans-serif;">
                        🔔 New Job Application
                      </td>
                    </tr>
                  </table>
                  
                  <h2 style="color: #333; font-family: Arial, sans-serif; margin: 0 0 10px 0;">New Job Application Received</h2>
                  <p style="color: #8B5A8C; font-size: 22px; font-weight: 600; margin: 10px 0; font-family: Arial, sans-serif;">Position: ${position}</p>
                  <p style="color: #8B5A8C; font-size: 18px; font-weight: 600; margin: 5px 0 20px 0; font-family: Arial, sans-serif;">Applicant: ${name}</p>
                  
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 0 0 15px 0;">A new job application has been submitted through your website. Please review the details below:</p>
                  
                  <!-- Attachment Note -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #fff3e0; border: 1px solid #ffcc02; border-radius: 8px; margin: 20px 0;">
                    <tr>
                      <td style="padding: 15px; color: #e65100; font-family: Arial, sans-serif;">
                        <strong>📎 Resume Attached:</strong> The applicant's resume is attached to this email for your review.
                      </td>
                    </tr>
                  </table>
                  
                  <!-- Application Data Table -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; margin: 25px 0; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <tbody>
                      ${applicationDataHtml}
                    </tbody>
                  </table>
                  
                  ${
                    applicationData.coverLetter
                      ? `
                  <!-- Cover Letter Section -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8f4ff; border: 1px solid #E6E6FA; border-radius: 12px; margin: 25px 0;">
                    <tr>
                      <td style="padding: 25px;">
                        <h3 style="color: #8B5A8C; margin: 0 0 15px 0; font-family: Arial, sans-serif;">Cover Letter / Additional Information:</h3>
                        <p style="margin: 0; white-space: pre-wrap; color: #333; font-family: Arial, sans-serif;">${applicationData.coverLetter}</p>
                      </td>
                    </tr>
                  </table>
                  `
                      : ""
                  }
                  
                  <!-- Quick Actions -->
                  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fdf9; border: 1px solid #E6E6FA; border-radius: 12px; margin: 25px 0;">
                    <tr>
                      <td style="padding: 20px;">
                        <h3 style="color: #8B5A8C; margin: 0 0 15px 0; font-family: Arial, sans-serif;">Quick Actions:</h3>
                        <table cellpadding="0" cellspacing="0">
                          <tr>
                            <td style="padding-right: 10px;">
                              <a href="mailto:${applicationData.email}?subject=Re: Your Application for ${position}" style="display: inline-block; background-color: #E6E6FA; color: #4A4A4A; text-decoration: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; font-family: Arial, sans-serif;">Reply to Applicant</a>
                            </td>
                            <td>
                              <a href="tel:${applicationData.phone}" style="display: inline-block; background-color: #FFC0CB; color: #4A4A4A; text-decoration: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; font-family: Arial, sans-serif;">Call Applicant</a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                  
                  <p style="color: #333; font-family: Arial, sans-serif; margin: 20px 0 10px 0;"><strong>Next Steps:</strong></p>
                  <ul style="margin: 0; padding-left: 20px; color: #333; font-family: Arial, sans-serif;">
                    <li>Review the attached resume and application details</li>
                    <li>Contact the applicant within 3-5 business days if interested</li>
                    <li>Schedule a phone screening if qualifications match</li>
                    <li>Arrange an in-person interview for qualified candidates</li>
                  </ul>
                </td>
              </tr>
              
              <!-- Footer -->
              <tr>
                <td style="background-color: #f8f4ff; padding: 25px; text-align: center; border-radius: 0 0 12px 12px; border-top: 1px solid #E6E6FA;">
                  <p style="margin: 0 0 10px 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">&copy; ${new Date().getFullYear()} Journey of Care. All rights reserved.</p>
                  <p style="margin: 0; font-size: 12px; color: #666; font-family: Arial, sans-serif;">This notification was generated automatically from your website's job application form.</p>
                </td>
              </tr>
              
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `
}
