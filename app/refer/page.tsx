"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ArrowLeft, Gift, Users, CheckCircle } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { Checkbox } from "@/components/ui/checkbox"

export default function ReferPage() {
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)
  const [smsConsent, setSmsConsent] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmissionError(null)

    // Get form data
    const form = e.target as HTMLFormElement
    const formData = new FormData(form)

    // Convert FormData to object
    const formValues: Record<string, string> = {}
    formData.forEach((value, key) => {
      formValues[key] = value.toString()
    })

    // Debug log to see what's being collected
    console.log("Form values:", formValues)

    try {
      // Send form data to our API
      const response = await fetch("/api/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formValues.yourName,
          email: formValues.yourEmail,
          phone: formValues.yourPhone || "Not provided",
          referralName: formValues.referralName,
          referralContact: formValues.referralContact,
          optionalMessage: formValues.optionalMessage || "Not provided",
          formType: "referral",
          smsConsent: smsConsent.toString(),
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setFormSubmitted(true)
        form.reset()
        setSmsConsent(false)
      } else {
        console.error("API error:", result)
        setSubmissionError(result.error || "There was a problem submitting your referral. Please try again.")
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      setSubmissionError("There was a problem submitting your referral. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Referral benefits
  const referralBenefits = [
    {
      title: "Help Your Loved Ones",
      description: "Connect your friends and family with quality home care services",
      icon: Users,
    },
    {
      title: "Simple Process",
      description: "Our referral process is quick and easy, taking just a few minutes of your time",
      icon: CheckCircle,
    },
    {
      title: "No Limit",
      description: "There's no limit to how many people you can refer to our services",
      icon: Gift,
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Refer a Friend — Get a Thank You"
        subtitle="Know someone who needs care? Send them to Aneeko Rapha Healthcare Services."
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1752924797/pexels-shvets-production-7176288_tvtzaj.jpg"
        imageAlt="People holding hands in support"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-5xl mx-auto">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Referral Program
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Refer a Friend — Get a Thank You
              </h1>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Know someone who needs care? Send them to Aneeko Rapha Healthcare Services. If they begin services, we'll send you a $25 Visa eGift card — no limit, no hassle, just gratitude.
              </p>

              <Dialog open={showModal} onOpenChange={setShowModal}>
                <DialogTrigger asChild>
                  <Button className="mt-6 bg-secondary hover:bg-secondary-dark text-white">
                    Learn More About Our Referral Program
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Referral Program Details</DialogTitle>
                    <DialogDescription>
                      Learn how our referral program works and how you can help others.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <h4 className="font-bold text-gray-800 dark:text-gray-100">How It Works</h4>
                    <ol className="list-decimal pl-5 space-y-2 text-gray-600 dark:text-gray-300">
                      <li>Refer a friend, family member, or acquaintance who needs home care services</li>
                      <li>Have them mention your name when they contact us</li>
                      <li>We'll reach out to them to discuss their care needs and how we can help</li>
                    </ol>

                    <h4 className="font-bold text-gray-800 dark:text-gray-100 mt-4">Why Refer?</h4>
                    <ul className="list-disc pl-5 space-y-2 text-gray-600 dark:text-gray-300">
                      <li>Help your loved ones receive quality home care</li>
                      <li>Support those who might not know where to turn for help</li>
                      <li>Be part of improving someone's quality of life</li>
                      <li>Share the positive experience you've had with our services</li>
                    </ul>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Benefits Cards */}
            <div className="grid md:grid-cols-3 gap-6 mb-16">
              {referralBenefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                  className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mb-4">
                      <benefit.icon className="h-8 w-8 text-primary dark:text-primary-light" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">{benefit.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 font-light">{benefit.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Referral Form */}
            <Card className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl mb-16">
              <CardContent className="p-8">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Refer Someone Today</h2>

                {formSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary-light/20 rounded-lg p-6 text-center"
                  >
                    <CheckCircle className="h-12 w-12 text-primary dark:text-primary-light mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-primary dark:text-primary-light mb-2">Thank You!</h3>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      Thank you for your referral to Aneeko Rapha Healthcare Services. We appreciate your trust in our services and
                      will reach out to your referral soon to discuss how we can help them with their home care needs in
                      Tarrant County, TX.
                    </p>
                    <Button
                      onClick={() => setFormSubmitted(false)}
                      variant="outline"
                      className="border-primary dark:border-primary-light text-primary dark:text-primary-light hover:bg-primary/10 dark:hover:bg-primary/20"
                    >
                      Refer Another Person
                    </Button>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Your Information</h3>

                        <div className="space-y-4">
                          <div>
                            <label htmlFor="yourName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Your Name *
                            </label>
                            <Input
                              id="yourName"
                              name="yourName"
                              required
                              className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                            />
                          </div>

                          <div>
                            <label htmlFor="yourEmail" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Your Email *
                            </label>
                            <Input
                              id="yourEmail"
                              name="yourEmail"
                              type="email"
                              required
                              className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                            />
                          </div>

                          <div>
                            <label htmlFor="yourPhone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Your Phone
                            </label>
                            <Input
                              id="yourPhone"
                              name="yourPhone"
                              className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                          Referred Person's Information
                        </h3>

                        <div className="space-y-4">
                          <div>
                            <label
                              htmlFor="referralName"
                              className="text-sm font-medium text-gray-700 dark:text-gray-300"
                            >
                              Referred Person's Name *
                            </label>
                            <Input
                              id="referralName"
                              name="referralName"
                              required
                              className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                            />
                          </div>

                          <div>
                            <label
                              htmlFor="referralContact"
                              className="text-sm font-medium text-gray-700 dark:text-gray-300"
                            >
                              Their Contact Info *
                            </label>
                            <Input
                              id="referralContact"
                              name="referralContact"
                              placeholder="Email or phone number"
                              required
                              className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="optionalMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Optional Message
                      </label>
                      <Textarea
                        id="optionalMessage"
                        name="optionalMessage"
                        placeholder="Any additional information you'd like to share..."
                        className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    {/* SMS Consent Checkbox */}
                    <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="smsConsent"
                          checked={smsConsent}
                          onCheckedChange={(checked) => setSmsConsent(checked as boolean)}
                          className="mt-1"
                        />
                        <label
                          htmlFor="smsConsent"
                          className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                        >
                          By checking this box, I consent to receive text messages related to appointment reminders,
                          follow-ups, billing inquiries, and service updates from Aneeko Rapha Healthcare Services. You can reply
                          "STOP" at any time to opt out. Message and data rates may apply. Message frequency may vary,
                          text HELP to (682) 228-9234 for assistance. For more information, please refer to our{" "}
                          <Link
                            href="/privacy-policy"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            privacy policy
                          </Link>
                          , and{" "}
                          <Link
                            href="/terms-conditions"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            Terms and Conditions
                          </Link>{" "}
                          on our website.
                        </label>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full rounded-lg bg-primary hover:bg-primary-dark text-white py-6 h-auto transition-all duration-300 hover:shadow-lg"
                    >
                      {isSubmitting ? "Submitting..." : "Submit Referral"}
                    </Button>

                    {submissionError && (
                      <p className="mt-4 text-red-600 dark:text-red-400 text-sm">{submissionError}</p>
                    )}
                  </form>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
