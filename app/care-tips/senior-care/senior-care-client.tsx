"use client"

import <PERSON> from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, Shield, Users, Clock, Home, Phone, Mail, ArrowRight, CheckCircle } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function SeniorCareClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Senior care tips
  const careTips = [
    {
      title: "Health Monitoring",
      tips: [
        "Regular medication management and scheduling",
        "Blood pressure and vital signs monitoring",
        "Coordination with healthcare providers",
        "Managing chronic conditions effectively",
        "Recognizing signs of health changes"
      ]
    },
    {
      title: "Safety & Mobility",
      tips: [
        "Fall prevention strategies and home modifications",
        "Proper lighting throughout the home",
        "Installing grab bars and safety equipment",
        "Regular vision and hearing checkups",
        "Emergency response systems"
      ]
    },
    {
      title: "Nutrition & Wellness",
      tips: [
        "Balanced meal planning for seniors",
        "Proper hydration and nutrition monitoring",
        "Social engagement and mental stimulation",
        "Regular physical activity appropriate for age",
        "Managing dietary restrictions and preferences"
      ]
    },
    {
      title: "Daily Living Support",
      tips: [
        "Personal hygiene and grooming assistance",
        "Household management and organization",
        "Transportation and mobility support",
        "Companionship and emotional support",
        "Technology assistance and communication"
      ]
    }
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Senior Care Guide"
        subtitle="Comprehensive tips for caring for elderly loved ones"
        imageSrc="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
        imageAlt="Senior care and support"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Senior Care
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Essential Senior Care Tips
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Caring for elderly loved ones requires knowledge, patience, and the right resources. 
                Our comprehensive guide provides practical tips to ensure the safety, health, and happiness of seniors.
              </p>
            </div>
          </motion.section>

          {/* Care Tips Grid */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid md:grid-cols-2 gap-8">
              {careTips.map((category, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                    <CardContent className="p-6 h-full">
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
                        {category.title}
                      </h3>
                      
                      <ul className="space-y-3">
                        {category.tips.map((tip, tipIndex) => (
                          <li key={tipIndex} className="flex items-start gap-3">
                            <CheckCircle className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                              {tip}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Key Benefits Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Benefits of Professional Senior Care
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                  Professional senior care services provide peace of mind for families while ensuring the highest quality of life for elderly loved ones.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                    <Heart className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Personalized Care</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Customized care plans tailored to individual needs and preferences
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-4 flex items-center justify-center">
                    <Shield className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Safety & Security</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Professional monitoring and emergency response capabilities
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-green-600 mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Family Support</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Relief for family caregivers and professional guidance
                  </p>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Ready to Get Started?
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Contact Aneeko Rapha Healthcare Services today to learn how we can help provide exceptional senior care for your loved one.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/care-tips">
                    <span>Back to Care Tips</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
