"use client"

import React from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Heart, Users, Shield, Award, Phone, Mail } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function AboutPageClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Core values data - Aneeko Rapha Healthcare Services
  const coreValues = [
    {
      icon: Heart,
      title: "Integrity & Empathy",
      description: "We provide exceptional healthcare services with integrity, empathy, and genuine care for every client.",
    },
    {
      icon: Users,
      title: "Personalized Care",
      description: "Every care plan is tailored to meet the unique needs and preferences of our clients.",
    },
    {
      icon: Shield,
      title: "Safety & Quality",
      description: "We maintain the highest standards of safety and quality in all our healthcare services.",
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We strive for excellence in everything we do, continuously improving our services.",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="About Aneeko Rapha Healthcare Services"
        subtitle="Providing compassionate home healthcare services in Tarrant County, TX"
        imageSrc="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
        imageAlt="Healthcare professionals providing care"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* About Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="space-y-6">
                <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium">
                  About Us
                </Badge>
                <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Exceptional Healthcare with Integrity
                </h1>
                <div className="w-20 h-1 bg-primary rounded-full"></div>
                <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    At Aneeko Rapha Healthcare Services, we are dedicated to providing exceptional home healthcare services with integrity, empathy, and excellence. Our mission is to deliver comprehensive and personalized care that meets the diverse needs of our clients in Tarrant County, TX.
                  </p>
                  <p>
                    Through continuous innovation, collaboration, and a commitment to excellence, we aim to improve health outcomes and empower individuals to live healthier, happier lives in the comfort of their own homes.
                  </p>
                </div>
              </div>

              <div className="relative">
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
                    alt="Healthcare team"
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Core Values Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                Our Values
              </Badge>
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                What Drives Us
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full"></div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {coreValues.map((value, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                    <CardContent className="p-8 text-center h-full flex flex-col">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6 flex items-center justify-center">
                        <value.icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed flex-grow">
                        {value.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Schedule a Service Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                Contact Aneeko Rapha Healthcare Services today to learn more about our services and schedule your free consultation.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/testimonials">
                    <span>Read Client Stories</span>
                  </Link>
                </Button>
                <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100 hover:text-gray-900 px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <a href="tel:6822289234">
                    <Phone className="h-5 w-5" />
                    <span>Call (*************</span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
