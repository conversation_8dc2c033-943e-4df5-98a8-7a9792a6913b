import { type NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"
import {
  JobApplicationEmailTemplate,
  AdminJobApplicationEmailTemplate,
} from "@/components/job-application-email-template"

export async function POST(request: NextRequest) {
  try {
    // Lazily create the Resend client so the constructor
    // isn’t evaluated at build-time (when env vars aren’t available).
    const resend = new Resend(process.env.RESEND_API_KEY ?? "")

    const formData = await request.formData()

    // Extract form fields
    const name = formData.get("name") as string
    const email = formData.get("email") as string
    const phone = formData.get("phone") as string
    const position = formData.get("position") as string
    const resume = formData.get("resume") as File

    // Optional fields
    const address = formData.get("address") as string
    const experience = formData.get("experience") as string
    const availability = formData.get("availability") as string
    const transportation = formData.get("transportation") as string
    const background = formData.get("background") as string
    const coverLetter = formData.get("coverLetter") as string

    // Validate required fields
    if (!name || !email || !phone || !position) {
      return NextResponse.json({ error: "Name, email, phone, and position are required" }, { status: 400 })
    }

    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>"
    const fromEmail = process.env.DEFAULT_FROM_EMAIL || "Journey of Care <<EMAIL>>"

    // Prepare application data
    const applicationData = {
      name,
      email,
      phone,
      position,
      address,
      experience,
      availability,
      transportation,
      background,
      coverLetter,
    }

    // Prepare resume attachment if provided
    const attachments = []
    if (resume && resume.size > 0) {
      const resumeBuffer = await resume.arrayBuffer()
      attachments.push({
        filename: resume.name,
        content: Buffer.from(resumeBuffer),
      })
    }

    // Send confirmation email to applicant
    const applicantEmailData = await resend.emails.send({
      from: fromEmail,
      to: [email],
      subject: `Journey of Care - Application Received for ${position}`,
      html: JobApplicationEmailTemplate({
        name,
        position,
        applicationData,
      }),
    })

    // Send notification email to admin with resume attachment
    const adminEmailData = await resend.emails.send({
      from: fromEmail,
      to: [adminEmail],
      subject: `Journey of Care - New Job Application: ${position} - ${name}`,
      html: AdminJobApplicationEmailTemplate({
        name,
        position,
        applicationData,
      }),
      attachments: attachments.length > 0 ? attachments : undefined,
    })

    console.log("Journey of Care - Job application emails sent successfully:", {
      applicantEmailData,
      adminEmailData,
    })

    return NextResponse.json(
      {
        message: `Thank you for applying to Journey of Care, ${name}! We've received your application for the ${position} position and will review it shortly.`,
        success: true,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("Journey of Care - Error processing job application:", error)
    return NextResponse.json(
      {
        error: "Failed to submit job application. Please try again or contact us directly at (832) 446-0705.",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
