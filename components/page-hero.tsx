import Image from "next/image"

interface PageHeroProps {
  title: string
  subtitle: string
  imageSrc: string
  imageAlt: string
}

export default function PageHero({ title, subtitle, imageSrc, imageAlt }: PageHeroProps) {
  return (
    <div className="relative h-[50vh] min-h-[400px] max-h-[500px] w-full overflow-hidden mt-[80px]">
      {/* Background Image */}
      <Image src={imageSrc || "/placeholder.svg"} alt={imageAlt} fill priority className="object-cover object-top" sizes="100vw" />

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>

      {/* Content */}
      <div className="absolute inset-0 flex items-center justify-center px-4">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-light text-white mb-4">{title}</h1>
          <p className="text-lg md:text-xl text-white/90">{subtitle}</p>
        </div>
      </div>
    </div>
  )
}
