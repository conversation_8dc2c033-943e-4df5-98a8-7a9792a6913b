"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Heart, Shield, Clock, Stethoscope, ArrowRight, Star, CheckCircle } from "lucide-react"
import { useInView } from "react-intersection-observer"
import { cn } from "@/lib/utils"

export default function PDNSection() {
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.1 })

  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  const pdnFeatures = [
    {
      icon: Stethoscope,
      title: "Skilled Nursing Care",
      description: "Licensed RNs providing medical care, medication management, and clinical assessments",
    },
    {
      icon: Heart,
      title: "Compassionate Support",
      description: "One-on-one attention with dignity, respect, and genuine care for each individual",
    },
    {
      icon: Shield,
      title: "Medical Expertise",
      description: "Complex medical condition management, wound care, and post-surgical support",
    },
    {
      icon: Clock,
      title: "24/7 Availability",
      description: "Round-the-clock care options to meet your specific scheduling needs",
    },
  ]

  return (
    <section
      ref={ref}
      className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-lavender-light to-pink-light dark:from-gray-900 dark:to-gray-950"
      id="pdn-services"
    >
      <motion.div
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        variants={fadeIn}
        className={cn(containerClass)}
      >
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-block">
            <Badge className="px-4 py-1.5 bg-gradient-to-r from-pink to-lavender text-white rounded-full text-sm font-medium mb-4">
              ⭐ Featured Service
            </Badge>
          </div>
          <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
            Private Duty Nursing (PDN)
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-pink to-lavender mx-auto rounded-full mb-6"></div>
          <p className="max-w-3xl mx-auto text-gray-700 dark:text-gray-300 text-lg font-light leading-relaxed">
            Offering skilled, compassionate one-on-one nursing care at home tailored to individual medical needs. Our
            Private Duty Nursing services provide the highest level of clinical expertise with the personal touch your
            loved one deserves.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Content */}
          <motion.div variants={fadeInLeft} className="space-y-8">
            <div className="bg-gradient-to-br from-lavender/30 to-pink/30 p-8 rounded-2xl border-2 border-lavender/50 shadow-lg">
              <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
                Specialized One-on-One Care
              </h3>
              <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                Our Private Duty Nursing services go beyond basic care. We provide comprehensive medical support for
                clients with complex health conditions, ensuring they receive the specialized attention they need in the
                comfort of their own home.
              </p>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-pink mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-100">Medication Management</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Expert administration and monitoring of complex medication regimens
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-lavender-dark mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-100">Chronic Disease Management</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Specialized care for diabetes, heart conditions, respiratory issues, and more
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-pink mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-100">Post-Surgical Care</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Recovery support and wound care following hospital discharge
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center lg:text-left">
              <Link href="/consultation">
                <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-8 py-6 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto lg:mx-0">
                  <span>Schedule PDN Consultation</span>
                  <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                </Button>
              </Link>
            </div>
          </motion.div>

          {/* Right Content - Client Image */}
          <motion.div variants={fadeInRight} className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://res.cloudinary.com/dvauarkh6/image/upload/v1749907544/20250614_1424_Child_on_Ventilator_simple_compose_01jxq9qbdze3389swzwffrr07y_owydpc.png"
                alt="Compassionate Private Duty Nursing care - One-on-one attention for clients with complex medical needs"
                width={600}
                height={400}
                className="object-cover w-full h-[500px]"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <p className="text-white text-lg font-medium mb-2">
                  Providing compassionate, skilled nursing care when it matters most
                </p>
                <p className="text-white/90 text-sm">
                  Our PDN services ensure your loved one receives the medical attention they deserve
                </p>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 bg-gradient-to-r from-pink to-lavender text-white px-4 py-2 rounded-full shadow-lg">
              <span className="text-sm font-semibold">RN-Led Care</span>
            </div>

            <div className="absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-lg border border-lavender/30">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-pink" />
                <span className="text-sm font-medium text-gray-800 dark:text-gray-100">Specialized Medical Care</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Features Grid */}
        <motion.div variants={fadeInUp} className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {pdnFeatures.map((feature, index) => (
            <Card
              key={index}
              className="border border-lavender/30 shadow-md hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm hover:scale-105"
            >
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-pink to-lavender mx-auto mb-4 flex items-center justify-center">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div variants={fadeInUp} className="mt-16 text-center">
          <div className="bg-gradient-to-r from-lavender/20 to-pink/20 p-8 rounded-2xl border border-lavender/30 max-w-4xl mx-auto">
            <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Experience the PDN Difference
            </h3>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-6 leading-relaxed">
              When your loved one needs specialized medical care at home, trust Illiana's Angel Healthcare's Private
              Duty Nursing services. Our registered nurses provide the clinical expertise and compassionate care your
              family deserves.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/services?service=pdn">
                <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg">
                  Learn More About PDN
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  variant="outline"
                  className="rounded-full border-2 border-lavender text-lavender-dark hover:bg-lavender hover:text-white px-6 py-2 h-auto transition-all duration-300"
                >
                  Contact Us Today
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute top-20 right-20 w-64 h-64 bg-lavender/20 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-40 left-20 w-80 h-80 bg-pink/20 rounded-full blur-3xl -z-10"></div>
    </section>
  )
}
