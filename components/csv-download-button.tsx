"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Download } from "lucide-react"
import { useState } from "react"

export function CsvDownloadButton() {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = async () => {
    try {
      setIsDownloading(true)
      const response = await fetch("/api/blog-csv")
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = "thrivewell-blog-posts.csv"
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error("Error downloading CSV:", error)
    } finally {
      setIsDownloading(false)
    }
  }

  return (
    <Button
      variant="outline"
      className="flex items-center gap-2 border-[#2e7d32] dark:border-[#4caf50] text-[#2e7d32] dark:text-[#4caf50] hover:bg-[#e8f5e9] dark:hover:bg-[#1b5e20]/20"
      onClick={handleDownload}
      disabled={isDownloading}
    >
      <Download className="h-4 w-4" />
      {isDownloading ? "Downloading..." : "Download Blog List CSV"}
    </Button>
  )
}
