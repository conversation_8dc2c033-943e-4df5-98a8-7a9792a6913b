"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, Users, Shield, Clock, Home, Phone, Mail, ArrowRight } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function CareClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Care tips data
  const careTopics = [
    {
      title: "Senior Care",
      description: "Comprehensive information about caring for elderly loved ones, including health monitoring, safety tips, and quality of life improvements.",
      icon: Users,
      link: "/care-tips/senior-care",
      color: "bg-primary/10 text-primary",
    },
    {
      title: "Respite Care",
      description: "Learn about respite care services that provide temporary relief for primary caregivers while ensuring continued quality care.",
      icon: Clock,
      link: "/care-tips/respite-care",
      color: "bg-secondary/10 text-secondary",
    },
    {
      title: "Home Safety",
      description: "Essential tips for creating a safe home environment for seniors, including fall prevention and emergency preparedness.",
      icon: Shield,
      link: "/care-tips/home-safety",
      color: "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400",
    },
    {
      title: "Health & Wellness",
      description: "Guidelines for maintaining physical and mental health, medication management, and promoting overall wellness.",
      icon: Heart,
      link: "/care-tips/health-wellness",
      color: "bg-red-50 text-red-600 dark:bg-red-900/10 dark:text-red-300",
    },
    {
      title: "Home Care Services",
      description: "Understanding different types of home care services and how to choose the right care plan for your loved one.",
      icon: Home,
      link: "/care-tips/home-care-services",
      color: "bg-blue-50 text-blue-600 dark:bg-blue-900/10 dark:text-blue-300",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Care Tips & Information"
        subtitle="Helpful guidance for families and caregivers"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg"
        imageAlt="Care tips and guidance"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Care Information
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Essential Care Tips & Guidance
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Access valuable information and practical tips to help you provide the best care for your loved ones. 
                Our comprehensive guides cover everything from senior care to home safety.
              </p>
            </div>
          </motion.section>

          {/* Care Topics Grid */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {careTopics.map((topic, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm group">
                    <CardContent className="p-6 h-full flex flex-col">
                      <div className="flex items-center mb-4">
                        <div className={`w-12 h-12 rounded-lg ${topic.color} flex items-center justify-center mr-4`}>
                          <topic.icon className="h-6 w-6" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 group-hover:text-primary transition-colors">
                          {topic.title}
                        </h3>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-6 flex-grow">
                        {topic.description}
                      </p>
                      
                      <Button className="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white rounded-lg transition-all duration-300 flex items-center justify-center gap-2 group" asChild>
                        <Link href={topic.link}>
                          <span>Learn More</span>
                          <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Need Personalized Care Guidance?
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Our experienced care coordinators are here to help you create a personalized care plan that meets your family's unique needs.
              </p>

              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6 flex items-center justify-center">
                    <Phone className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Call for Consultation</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Speak with our care experts to discuss your specific needs and get personalized recommendations.
                  </p>
                  <a href="tel:6822289234" className="text-2xl font-bold text-primary hover:text-primary-dark transition-colors">
                    (*************
                  </a>
                </div>

                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-6 flex items-center justify-center">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Email Us</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Send us your questions and we'll provide detailed information about our care services.
                  </p>
                  <a href="mailto:<EMAIL>" className="text-lg font-semibold text-secondary hover:text-secondary-dark transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/services">
                    <span>View Our Services</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
