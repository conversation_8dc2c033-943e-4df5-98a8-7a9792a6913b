import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { email, name } = await request.json()

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 })
    }

    // Here you would typically integrate with your email service provider
    // For example, Mailchimp, ConvertKit, or Brevo (Sendinblue)

    // Example with Brevo API
    if (process.env.BREVO_API_KEY) {
      const response = await fetch("https://api.brevo.com/v3/contacts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": process.env.BREVO_API_KEY,
        },
        body: JSON.stringify({
          email,
          attributes: {
            FIRSTNAME: name || "",
            COMPANY: "Journey of Care",
            SERVICE_AREA: "Conroe, TX",
            SOURCE: "Journey of Care Website",
            SIGNUP_DATE: new Date().toISOString(),
          },
          listIds: [1], // Replace with your actual list ID
          updateEnabled: true,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Journey of Care - Brevo API error:", errorData)

        // If contact already exists, that's actually success
        if (errorData.code === "duplicate_parameter") {
          return NextResponse.json({
            message: "You're already subscribed to Journey of Care updates! Thank you for your continued interest.",
            coupon: "JOURNEY5",
          })
        }

        throw new Error(`Brevo API error: ${errorData.message}`)
      }
    }

    // Return success response with Journey of Care branding
    return NextResponse.json({
      message:
        "Thank you for subscribing to Journey of Care updates! We'll keep you informed about our home care services and helpful resources.",
      coupon: "JOURNEY5", // 5% discount coupon code for Journey of Care
      success: true,
    })
  } catch (error) {
    console.error("Journey of Care - Newsletter subscription error:", error)
    return NextResponse.json(
      {
        error:
          "Failed to subscribe to Journey of Care newsletter. Please try again or contact us at (832) 446-0705.",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
