"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, ChevronDown, Phone, X, MapPin, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

// Services list for Aneeko Rapha Healthcare Services
const servicesList = [
  { title: "24/7 Care Services", href: "/services?service=24-hour-care" },
  { title: "Companionship", href: "/services?service=companionship" },
  { title: "Dementia Care", href: "/services?service=dementia-care" },
  { title: "Homemaking", href: "/services?service=homemaking" },
  { title: "Personal Care", href: "/services?service=personal-care" },
  { title: "Post Surgery Care", href: "/services?service=post-surgery-care" },
  { title: "Respite Care", href: "/services?service=respite-care" },
]

// Company list for Aneeko Rapha Healthcare Services
const companyList = [
  { title: "Careers", href: "/careers" },
  { title: "Job Openings", href: "/careers/jobs" },
  { title: "Testimonials", href: "/testimonials" },
]

// Resources list for Aneeko Rapha Healthcare Services
const resourcesList = [
  { title: "AARP", href: "https://www.aarp.org/", external: true },
  { title: "U.S. Department of Health & Human Services", href: "https://www.hhs.gov/", external: true },
  { title: "National Institute of Aging", href: "https://www.nia.nih.gov/", external: true },
  { title: "Administration on Aging", href: "http://www.acl.gov", external: true },
  { title: "Meals on Wheels America", href: "http://www.mealsonwheelsamerica.org", external: true },
  { title: "Alzheimer's Association", href: "http://www.alz.org", external: true },
]

export default function Header() {
  const [scrolled, setScrolled] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)
  const [companyDropdownOpen, setCompanyDropdownOpen] = useState(false)
  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(false)

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close menu when clicking outside
  useEffect(() => {
    if (!mobileMenuOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.body.style.overflow = "hidden"

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = ""
    }
  }, [mobileMenuOpen])

  // Handle clicks outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      // Check if click is outside all dropdown containers
      if (target && !target.closest('.dropdown-container')) {
        setServicesDropdownOpen(false)
        setCompanyDropdownOpen(false)
        setResourcesDropdownOpen(false)
      }
    }

    if (servicesDropdownOpen || companyDropdownOpen || resourcesDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [servicesDropdownOpen, companyDropdownOpen, resourcesDropdownOpen])

  // Handle close button click
  const handleCloseMenu = () => {
    setMobileMenuOpen(false)
    setServicesDropdownOpen(false)
    setCompanyDropdownOpen(false)
    setResourcesDropdownOpen(false)
  }

  // Handle mobile menu toggle
  const handleToggleMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  // Professional animation variants
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    },
  }

  const navItem = {
    hidden: { opacity: 0, y: -8 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4
      }
    },
  }

  // Refined mobile menu animations
  const menuVariants = {
    closed: {
      opacity: 0,
      x: "100%"
    },
    open: {
      opacity: 1,
      x: 0
    },
  }

  const menuItemVariants = {
    closed: { opacity: 0, x: 20 },
    open: {
      opacity: 1,
      x: 0
    },
  }

  // Navigation items - Updated for Aneeko Rapha Healthcare Services (5 menus only)
  const navItems = [
    { name: "About", href: "/about" },
    {
      name: "Services",
      href: "/services",
      hasDropdown: true,
      dropdownType: "services",
    },
    {
      name: "Company",
      href: "/careers",
      hasDropdown: true,
      dropdownType: "company",
    },
    {
      name: "Resources",
      href: "/resources",
      hasDropdown: true,
      dropdownType: "resources",
    },
    { name: "Contact", href: "/contact" },
  ]

  // Professional container class for consistent spacing
  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"



  // Add body class when dropdown is open to prevent scrollbar issues
  useEffect(() => {
    if (servicesDropdownOpen) {
      document.body.classList.add("dropdown-open")
    } else {
      document.body.classList.remove("dropdown-open")
    }

    return () => {
      document.body.classList.remove("dropdown-open")
    }
  }, [servicesDropdownOpen])

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-[9000] transition-all duration-500 ease-out w-full ${
        scrolled
          ? "bg-white/95 dark:bg-gray-950/95 backdrop-blur-md shadow-lg border-b border-gray-200/20 dark:border-gray-700/20"
          : "bg-white dark:bg-gray-950"
      }`}
    >
      {/* Top Contact Bar - Professional info strip */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-gray-900 dark:to-gray-800 border-b border-gray-200/30 dark:border-gray-700/30 hidden lg:block">
        <div className={`${containerClass} py-2`}>
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center gap-6 text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-2">
                <MapPin className="h-3.5 w-3.5" />
                <span>Serving Tarrant County, TX</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-3.5 w-3.5" />
                <span>24/7 Care Available</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className={`${containerClass} py-4 lg:py-5`}>
        <div className="flex justify-between items-center">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="flex items-center"
          >
            <Link href="/" className="flex items-center group">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="relative transition-transform duration-300 group-hover:scale-105"
              >
                <Image
                  src="/logo.png"
                  alt="Aneeko Rapha Healthcare Services - Your Trusted Home Care Agency"
                  width={300}
                  height={100}
                  className="h-auto w-auto max-h-14 lg:max-h-16"
                  priority
                />
              </motion.div>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <motion.nav
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
            className="hidden lg:flex items-center gap-8"
          >
            {navItems.map((item) => (
              <motion.div key={item.name} variants={navItem}>
                {item.hasDropdown ? (
                  <div className="relative group dropdown-container">
                    <button
                      className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium text-base transition-all duration-300 flex items-center gap-1 py-2 group focus:outline-none"
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        if (item.dropdownType === "services") {
                          setServicesDropdownOpen(!servicesDropdownOpen)
                          setCompanyDropdownOpen(false)
                          setResourcesDropdownOpen(false)
                        } else if (item.dropdownType === "company") {
                          setCompanyDropdownOpen(!companyDropdownOpen)
                          setServicesDropdownOpen(false)
                          setResourcesDropdownOpen(false)
                        } else if (item.dropdownType === "resources") {
                          setResourcesDropdownOpen(!resourcesDropdownOpen)
                          setServicesDropdownOpen(false)
                          setCompanyDropdownOpen(false)
                        }
                      }}
                      aria-expanded={item.dropdownType === "services" ? servicesDropdownOpen : resourcesDropdownOpen}
                      aria-haspopup="true"
                    >
                      {item.name}
                      <ChevronDown className="h-4 w-4 transition-transform duration-300 group-hover:rotate-180" />
                    </button>
                    {((item.dropdownType === "services" && servicesDropdownOpen) ||
                      (item.dropdownType === "company" && companyDropdownOpen) ||
                      (item.dropdownType === "resources" && resourcesDropdownOpen)) && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 shadow-2xl rounded-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden"
                        style={{ zIndex: 9999 }}
                      >
                        <div className="p-2">
                          {(item.dropdownType === "services" ? servicesList :
                            item.dropdownType === "company" ? companyList : resourcesList).map((listItem, index) => (
                            <Link
                              key={index}
                              href={listItem.href}
                              {...((listItem as any).external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                              className="block px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-all duration-200 font-medium flex items-center justify-between"
                              onClick={() => {
                                // Only close dropdown after a small delay to allow navigation
                                setTimeout(() => {
                                  setServicesDropdownOpen(false)
                                  setCompanyDropdownOpen(false)
                                  setResourcesDropdownOpen(false)
                                }, 100)
                              }}
                            >
                              <span>{listItem.title}</span>
                              {(listItem as any).external && (
                                <svg className="h-4 w-4 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                              )}
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium text-base transition-all duration-300 py-2 relative group"
                  >
                    {item.name}
                    <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary dark:bg-primary-light transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                )}
              </motion.div>
            ))}
          </motion.nav>

          {/* Contact & CTA Section */}
          <div className="flex items-center gap-4">
            {/* Phone Number */}
            <motion.a
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              href="tel:6822289234"
              className="hidden sm:flex items-center text-primary hover:text-primary-dark font-semibold transition-all duration-300 group"
            >
              <Phone className="h-4 w-4 mr-2 transition-transform duration-300 group-hover:scale-110" />
              <span className="text-lg">(*************</span>
            </motion.a>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="hidden md:block"
            >
              <Link href="/consultation">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold px-6 py-2.5 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  Free Consultation
                </Button>
              </Link>
            </motion.div>

            {/* Mobile Menu Button */}
            <motion.button
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              onClick={handleToggleMenu}
              className="lg:hidden w-10 h-10 rounded-lg flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300"
              aria-label="Toggle Menu"
            >
              <Menu className="h-5 w-5" />
            </motion.button>
          </div>
        </div>
      </div>



      {/* Professional Mobile Menu Overlay */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            ref={menuRef}
            initial="closed"
            animate="open"
            exit="closed"
            variants={menuVariants}
            className="fixed inset-0 z-[9999] bg-white dark:bg-gray-950 lg:hidden"
            style={{ top: 0 }}
          >
            {/* Mobile Menu Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-gray-900 dark:to-gray-800">
              <Link href="/" onClick={handleCloseMenu} className="flex items-center">
                <Image
                  src="/logo.png"
                  alt="Aneeko Rapha Healthcare Services Logo"
                  width={200}
                  height={67}
                  className="h-auto w-auto max-h-10"
                  priority
                />
              </Link>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCloseMenu}
                className="w-10 h-10 rounded-lg flex items-center justify-center bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg transition-all duration-300"
                aria-label="Close Menu"
              >
                <X className="h-5 w-5" />
              </motion.button>
            </div>

            {/* Professional Mobile Menu Content */}
            <div className="flex flex-col h-full">
              <div className="flex-1 overflow-y-auto p-6">
                <motion.nav
                  variants={staggerContainer}
                  initial="hidden"
                  animate="visible"
                  className="space-y-2"
                >
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      variants={menuItemVariants}
                      transition={{ delay: index * 0.08 }}
                    >
                      {item.hasDropdown ? (
                        <div className="space-y-2">
                          <Link
                            href={item.href}
                            onClick={handleCloseMenu}
                            className="flex items-center justify-between px-4 py-4 text-lg font-semibold text-gray-800 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 rounded-xl transition-all duration-300"
                          >
                            {item.name}
                            <ChevronDown className="h-5 w-5" />
                          </Link>
                          <div className="ml-4 space-y-1 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                            {(item.dropdownType === "services" ? servicesList :
                              item.dropdownType === "company" ? companyList : resourcesList).map((listItem, listIndex) => (
                              <Link
                                key={listIndex}
                                href={listItem.href}
                                {...((listItem as any).external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                                onClick={handleCloseMenu}
                                className="flex items-center justify-between px-4 py-3 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg transition-all duration-300 font-medium"
                              >
                                <span>{listItem.title}</span>
                                {(listItem as any).external && (
                                  <svg className="h-4 w-4 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                )}
                              </Link>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          onClick={handleCloseMenu}
                          className="block px-4 py-4 text-lg font-semibold text-gray-800 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 rounded-xl transition-all duration-300"
                        >
                          {item.name}
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </motion.nav>
              </div>

              {/* Professional Mobile Menu Footer */}
              <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-gray-900 dark:to-gray-800 space-y-4">
                {/* Contact Information */}
                <div className="space-y-3">
                  <a
                    href="tel:6822289234"
                    className="flex items-center justify-center text-primary hover:text-primary-dark font-semibold transition-all duration-300 group"
                  >
                    <Phone className="h-5 w-5 mr-3 transition-transform duration-300 group-hover:scale-110" />
                    <span className="text-xl">(*************</span>
                  </a>

                  <div className="flex items-center justify-center text-gray-600 dark:text-gray-400 text-sm">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>24/7 Care Available</span>
                  </div>
                </div>

                {/* Professional CTA */}
                <Link href="/consultation" onClick={handleCloseMenu}>
                  <Button className="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold px-6 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    Schedule Free Consultation
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
