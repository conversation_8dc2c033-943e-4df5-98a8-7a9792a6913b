"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Heart, Users, Shield, Phone, Mail, ArrowRight, Clock } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { cn } from "@/lib/utils"

export default function ResourcesClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"

  // Resources data with specific images
  const resources = [
    {
      title: "American Association of Retired Persons (AARP)",
      description: "The nation's largest nonprofit organization dedicated to empowering Americans 50 and older.",
      url: "https://www.aarp.org/",
      category: "General Senior Resources",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/968e9-asi_logo-1.jpg",
      icon: Users,
    },
    {
      title: "U.S. Department of Health & Human Services",
      description: "Federal agency providing health and human services information and programs.",
      url: "https://www.hhs.gov/",
      category: "Government Resources",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/01a63-jh.png",
      icon: Shield,
    },
    {
      title: "National Institute of Aging",
      description: "Leading scientific research on aging and age-related diseases.",
      url: "https://www.nia.nih.gov/",
      category: "Health & Medical",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/85587-nia-logo.jpg",
      icon: Heart,
    },
    {
      title: "Administration on Aging",
      description: "Federal agency supporting older adults and their families with services and programs.",
      url: "http://www.acl.gov",
      category: "Government Resources",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/67ce3-administration_for_community_living_logo.svg_.png",
      icon: Shield,
    },
    {
      title: "Meals on Wheels America",
      description: "Nationwide network providing nutritious meals and social connection to seniors.",
      url: "http://www.mealsonwheelsamerica.org",
      category: "Nutrition & Support",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/b561d-mow_america_horz3line_rgb.png",
      icon: Heart,
    },
    {
      title: "Alzheimer's Association",
      description: "Leading voluntary health organization in Alzheimer's care, support and research.",
      url: "http://www.alz.org",
      category: "Health & Medical",
      image: "https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/0d48c-alzstacked_rgb.jpg",
      icon: Heart,
    },
  ]

  const categories = [
    { name: "General Senior Resources", color: "bg-primary/10 text-primary" },
    { name: "Government Resources", color: "bg-secondary/10 text-secondary" },
    { name: "Health & Medical", color: "bg-red-50 text-red-600 dark:bg-red-900/10 dark:text-red-300" },
    { name: "Nutrition & Support", color: "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400" },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Helpful Resources for Seniors & Families"
        subtitle="Access valuable information and support from trusted organizations"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg"
        imageAlt="Senior resources and support"
      />

      {/* Main Content */}
      <main className="py-24 relative z-10">
        <div className={cn(containerClass)}>
          {/* Introduction Section */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-16">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Helpful Resources
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Resources for Seniors & Families
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                At Aneeko Rapha Healthcare Services, we believe in empowering families with access to valuable resources and information. 
                Below you'll find links to trusted organizations that provide support, information, and services for seniors and their families.
              </p>
            </div>
          </motion.section>

          {/* Categories Filter */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-12">
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              {categories.map((category, index) => (
                <Badge key={index} className={`px-4 py-2 ${category.color} rounded-full text-sm font-medium`}>
                  {category.name}
                </Badge>
              ))}
            </div>
          </motion.section>

          {/* Care Tips Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Care Tips & Information
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Access our comprehensive care guides and helpful tips for families and caregivers.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
              <Card className="border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm group">
                <CardContent className="p-6">
                  <div className="w-12 h-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center mb-4">
                    <Users className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 group-hover:text-primary transition-colors">
                    Senior Care Tips
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4">
                    Comprehensive guide to caring for elderly loved ones, including health monitoring and safety tips.
                  </p>
                  <Button className="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white rounded-lg transition-all duration-300" asChild>
                    <Link href="/care-tips/senior-care">
                      Learn More
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm group">
                <CardContent className="p-6">
                  <div className="w-12 h-12 rounded-lg bg-secondary/10 text-secondary flex items-center justify-center mb-4">
                    <Clock className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 group-hover:text-secondary transition-colors">
                    Respite Care
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4">
                    Learn about respite care services that provide temporary relief for primary caregivers.
                  </p>
                  <Button className="w-full bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white rounded-lg transition-all duration-300" asChild>
                    <Link href="/care-tips/respite-care">
                      Learn More
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm group">
                <CardContent className="p-6">
                  <div className="w-12 h-12 rounded-lg bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 flex items-center justify-center mb-4">
                    <Heart className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 group-hover:text-green-600 transition-colors">
                    All Care Tips
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4">
                    Browse our complete collection of care tips, guides, and helpful information.
                  </p>
                  <Button variant="outline" className="w-full border-green-500 text-green-600 hover:bg-green-500 hover:text-white rounded-lg transition-all duration-300" asChild>
                    <Link href="/care-tips">
                      View All Tips
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </motion.section>

          {/* Resources Grid */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                External Resources & Organizations
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-3xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light leading-relaxed">
                Helpful links to trusted organizations that provide additional support and information.
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {resources.map((resource, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="h-full border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm group">
                    <CardContent className="p-6 h-full flex flex-col">
                      <div className="flex items-start justify-between mb-4">
                        <div className="w-16 h-16 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center overflow-hidden">
                          <img
                            src={resource.image}
                            alt={`${resource.title} logo`}
                            className="w-full h-full object-contain"
                            onError={(e) => {
                              // Fallback to icon if image fails to load
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const iconDiv = target.parentElement;
                              if (iconDiv) {
                                iconDiv.innerHTML = `<div class="w-12 h-12 rounded-lg bg-gradient-to-r from-primary to-primary-dark flex items-center justify-center"><svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg></div>`;
                              }
                            }}
                          />
                        </div>
                        <Badge className={`px-3 py-1 text-xs ${
                          resource.category === "General Senior Resources" ? "bg-primary/10 text-primary" :
                          resource.category === "Government Resources" ? "bg-secondary/10 text-secondary" :
                          resource.category === "Health & Medical" ? "bg-red-50 text-red-600 dark:bg-red-900/10 dark:text-red-300" :
                          "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                        }`}>
                          {resource.category}
                        </Badge>
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 group-hover:text-primary transition-colors">
                        {resource.title}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-6 flex-grow">
                        {resource.description}
                      </p>
                      
                      <a
                        href={resource.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center w-full px-4 py-2 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white rounded-lg transition-all duration-300 text-sm font-medium group"
                      >
                        <span>Visit Website</span>
                        <ExternalLink className="h-4 w-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                      </a>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Mission Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium">
                  Our Mission
                </Badge>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Exceptional Healthcare with Integrity
                </h2>
                <div className="w-20 h-1 bg-primary rounded-full"></div>
                <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    At Aneeko Rapha Health Care Services, our mission is to provide exceptional healthcare services with integrity, empathy, and excellence. We strive to deliver comprehensive and personalized care that meets the diverse needs of our patients.
                  </p>
                  <p>
                    Through continuous innovation, collaboration, and a commitment to excellence, we aim to improve health outcomes and empower individuals to live healthier, happier lives.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/590e8-55-2.jpg"
                    alt="Healthcare mission"
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Vision Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="relative order-2 lg:order-1"
              >
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://araphahealthcare.wordpress.com/wp-content/uploads/2025/04/0f2ce-aa.png"
                    alt="Healthcare vision"
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-6 order-1 lg:order-2"
              >
                <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium">
                  Our Vision
                </Badge>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">
                  Leading Provider of Compassionate Care
                </h2>
                <div className="w-20 h-1 bg-secondary rounded-full"></div>
                <div className="space-y-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    To become the leading provider of accessible, innovative, and compassionate healthcare solutions, enhancing the well-being of individuals and communities.
                  </p>
                  <p>
                    We envision a future where quality healthcare is accessible to all, delivered with innovation and compassion in the comfort of one's own home.
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Schedule a Service Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn} className="mb-20">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Schedule a Service
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Ready to experience the Aneeko Rapha difference? Contact us today to schedule your free consultation and learn how we can provide personalized care for your loved one in Tarrant County, TX.
              </p>

              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6 flex items-center justify-center">
                    <Phone className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Call for Immediate Service</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Speak directly with our care coordinators to discuss your needs and schedule services.
                  </p>
                  <a href="tel:**********" className="text-2xl font-bold text-primary hover:text-primary-dark transition-colors">
                    (*************
                  </a>
                </div>

                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-6 flex items-center justify-center">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Email Consultation</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Send us your questions and we'll respond with detailed information about our services.
                  </p>
                  <a href="mailto:<EMAIL>" className="text-lg font-semibold text-secondary hover:text-secondary-dark transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                    <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <Link href="/services">
                    <span>View All Services</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>

          {/* Contact Section */}
          <motion.section initial="hidden" whileInView="visible" variants={fadeIn}>
            <div className="bg-gradient-to-r from-secondary/5 to-primary/5 dark:from-secondary/10 dark:to-primary/10 rounded-2xl p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Need Help Finding Resources?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                Our team at Aneeko Rapha Healthcare Services is here to help you navigate available resources and find the support you need.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2 group" asChild>
                  <a href="tel:**********">
                    <Phone className="h-5 w-5" />
                    <span>Call (*************</span>
                  </a>
                </Button>
                <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 rounded-lg transition-all duration-300 flex items-center gap-2" asChild>
                  <Link href="/contact">
                    <Mail className="h-5 w-5" />
                    <span>Contact Us</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
