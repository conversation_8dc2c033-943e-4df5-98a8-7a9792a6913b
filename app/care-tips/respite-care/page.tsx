import type { Metadata } from "next"
import RespiteCareClient from "./respite-care-client"

export const metadata: Metadata = {
  title: "Respite Care Information | Aneeko Rapha Healthcare Services",
  description:
    "Learn about respite care services that provide temporary relief for primary caregivers while ensuring continued quality care for your loved one.",
  keywords: [
    "respite care",
    "caregiver relief",
    "temporary care",
    "family caregiver support",
    "short-term care",
    "caregiver respite",
  ],
  alternates: {
    canonical: "/care-tips/respite-care",
  },
  openGraph: {
    title: "Respite Care Information | Aneeko Rapha Healthcare Services",
    description:
      "Learn about respite care services that provide temporary relief for primary caregivers while ensuring continued quality care for your loved one.",
    url: "https://araphahealthcare.com/care-tips/respite-care",
  },
}

export default function RespiteCarePage() {
  return <RespiteCareClient />
}
