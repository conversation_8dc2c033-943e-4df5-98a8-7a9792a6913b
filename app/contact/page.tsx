"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Phone, Mail, MapPin } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"

export default function ContactPage() {
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)
  const [smsConsent, setSmsConsent] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmissionError(null)

    // Get form data
    const form = e.target as HTMLFormElement
    const formData = new FormData(form)

    // Convert FormData to object
    const formValues: Record<string, string> = {}
    formData.forEach((value, key) => {
      formValues[key] = value.toString()
    })

    // Add SMS consent
    formValues.smsConsent = smsConsent.toString()

    // Debug log to see what's being collected
    console.log("Form values:", formValues)

    try {
      // Send form data to our API
      const response = await fetch("/api/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formValues.name,
          email: formValues.email,
          phone: formValues.phone || "Not provided",
          subject: formValues.subject,
          message: formValues.message,
          smsConsent: formValues.smsConsent,
          formType: "contact",
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setFormSubmitted(true)
        form.reset()
        setSmsConsent(false)
      } else {
        console.error("API error:", result)
        setSubmissionError(result.error || "There was a problem sending your message. Please try again.")
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      setSubmissionError("There was a problem sending your message. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Contact information for Aneeko Rapha Healthcare Services
  const contactInfo = [
    {
      label: "Phone",
      value: "(*************",
      icon: Phone,
    },
    {
      label: "Email",
      value: "<EMAIL>",
      icon: Mail,
    },
    {
      label: "Service Area",
      value: "Tarrant County, TX",
      icon: MapPin,
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Contact Aneeko Rapha Healthcare Services"
        subtitle="We're here to answer your questions about home care services in Tarrant County, TX and surrounding areas."
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1753009413/pexels-olly-3781524_wyxspt.jpg"
        imageAlt="Person making a phone call"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          {/* Schedule a Service Section */}
          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-6xl mx-auto mb-16">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-2xl p-8 md:p-12 text-center">
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Schedule a Service with Aneeko Rapha Healthcare Services
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 max-w-3xl mx-auto">
                Ready to get started with exceptional home care services? Contact us today to schedule your free consultation and learn how we can provide personalized care for your loved one in Tarrant County, TX.
              </p>

              <div className="grid md:grid-cols-3 gap-8 mb-8">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                    <Phone className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Call for Immediate Service</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    Speak directly with our care coordinators
                  </p>
                  <a href="tel:**********" className="text-xl font-bold text-primary hover:text-primary-dark transition-colors">
                    (*************
                  </a>
                </div>

                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-secondary to-secondary-dark mx-auto mb-4 flex items-center justify-center">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Email Consultation</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    Send us your questions and requirements
                  </p>
                  <a href="mailto:<EMAIL>" className="text-lg font-semibold text-secondary hover:text-secondary-dark transition-colors">
                    <EMAIL>
                  </a>
                </div>

                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4 flex items-center justify-center">
                    <MapPin className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Service Area</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    We serve all of Tarrant County
                  </p>
                  <p className="text-lg font-semibold text-primary">Tarrant County, TX</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 group text-lg" asChild>
                  <Link href="/consultation">
                    <span>Schedule Free Consultation</span>
                  </Link>
                </Button>
                <Button className="bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-white px-8 py-4 rounded-lg transition-all duration-300 flex items-center gap-2 text-lg" asChild>
                  <a href="tel:**********">
                    <Phone className="h-5 w-5" />
                    <span>Call Now</span>
                  </a>
                </Button>
              </div>
            </div>
          </motion.div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-5xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6 text-center">
              Contact Aneeko Rapha Healthcare Services
            </h2>

            <p className="text-gray-600 dark:text-gray-300 mb-12 text-center">
              We're here to answer your questions and provide the support you need for home care services in Tarrant County, TX. Reach out to us through any of the methods below or fill out the contact form.
            </p>

            <div className="grid md:grid-cols-2 gap-12 items-start">
              <motion.div variants={fadeInLeft} className="space-y-8">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Send Us a Message</h2>

                {formSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary-light/20 rounded-lg p-6 text-center"
                  >
                    <h3 className="text-xl font-medium text-primary dark:text-primary-light mb-2">Thank You!</h3>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      Your message has been sent successfully to Aneeko Rapha Healthcare Services. We'll get back to you as soon as possible to discuss your home care needs.
                    </p>
                    <Button
                      onClick={() => setFormSubmitted(false)}
                      variant="outline"
                      className="border-primary dark:border-primary-light text-primary dark:text-primary-light hover:bg-primary/10 dark:hover:bg-primary/20"
                    >
                      Send Another Message
                    </Button>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Your Name *
                        </label>
                        <Input
                          id="name"
                          name="name"
                          required
                          className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                        />
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Email Address *
                        </label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phone Number
                      </label>
                      <Input
                        id="phone"
                        name="phone"
                        className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="subject" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Subject *
                      </label>
                      <Input
                        id="subject"
                        name="subject"
                        required
                        className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Your Message *
                      </label>
                      <Textarea
                        id="message"
                        name="message"
                        required
                        className="min-h-[120px] rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    {/* SMS Consent Checkbox */}
                    <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="smsConsent"
                          checked={smsConsent}
                          onCheckedChange={(checked) => setSmsConsent(checked as boolean)}
                          className="mt-1"
                        />
                        <label
                          htmlFor="smsConsent"
                          className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                        >
                          By checking this box, I consent to receive text messages related to my inquiry, appointment
                          scheduling, and care updates from Aneeko Rapha Healthcare Services. You can reply "STOP" at any time to opt
                          out. Message and data rates may apply. Message frequency may vary, text HELP to (*************
                          for assistance. For more information, please refer to our{" "}
                          <Link
                            href="/privacy-policy"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            privacy policy
                          </Link>
                          , and{" "}
                          <Link
                            href="/terms-conditions"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            Terms and Conditions
                          </Link>{" "}
                          on our website.
                        </label>
                      </div>
                    </div>

                    {submissionError && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <p className="text-red-700 dark:text-red-300 text-sm">{submissionError}</p>
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary hover:bg-primary-dark text-white py-3 rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? "Sending..." : "Send Message"}
                    </Button>
                  </form>
                )}
              </motion.div>

              <motion.div variants={fadeInRight} className="space-y-8">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Get in Touch</h2>

                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                        <info.icon className="h-6 w-6 text-primary dark:text-primary-light" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-1">{info.label}</h4>
                        <p className="text-gray-600 dark:text-gray-300">{info.value}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="bg-secondary/10 dark:bg-secondary/20 p-6 rounded-xl border border-secondary/10 dark:border-secondary-light/10">
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">
                    Schedule Your Free In-Home Care Assessment
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    Ready to learn more about our home care services? Contact Aneeko Rapha Healthcare Services today to schedule your
                    complimentary in-home assessment. We'll discuss your needs and create a personalized care plan for
                    your loved one.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button className="bg-secondary hover:bg-secondary-dark text-white" asChild>
                      <Link href="tel:**********">Call (*************</Link>
                    </Button>
                    <Button
                      variant="outline"
                      className="border-secondary text-secondary hover:bg-secondary/10 bg-transparent"
                      asChild
                    >
                      <Link href="mailto:<EMAIL>">Email Us</Link>
                    </Button>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">Office Hours</h3>
                  <div className="space-y-2 text-gray-600 dark:text-gray-300">
                    <p>
                      <strong>Monday - Friday:</strong> 8:00 AM - 5:00 PM
                    </p>
                    <p>
                      <strong>Saturday:</strong> On-call for emergencies
                    </p>
                    <p>
                      <strong>Sunday:</strong> On-call for emergencies
                    </p>
                    <p className="text-sm mt-3 text-gray-500 dark:text-gray-400">
                      *24/7 care services available upon request
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
